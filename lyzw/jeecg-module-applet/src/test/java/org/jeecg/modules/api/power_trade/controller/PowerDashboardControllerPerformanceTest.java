package org.jeecg.modules.api.power_trade.controller;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.api.power_trade.dto.DashboardSummaryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * PowerDashboardController性能测试
 * 验证并行优化后的性能提升效果
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class PowerDashboardControllerPerformanceTest {

    @Autowired
    private PowerDashboardController powerDashboardController;

    /**
     * 单次查询性能测试
     */
    @Test
    public void testSingleQueryPerformance() {
        log.info("=== 单次查询性能测试开始 ===");
        
        // 测试省份ID
        Integer[] provinceIds = {1, 2};
        
        for (Integer provinceId : provinceIds) {
            // 预热
            warmUp(provinceId);
            
            // 性能测试
            long startTime = System.currentTimeMillis();
            Result<DashboardSummaryDTO> result = powerDashboardController.getDashboard(provinceId);
            long duration = System.currentTimeMillis() - startTime;
            
            log.info("省份ID: {}, 查询耗时: {}ms, 成功: {}", 
                    provinceId, duration, result.isSuccess());
            
            // 验证结果
            assert result.isSuccess() : "查询应该成功";
            assert result.getResult() != null : "结果不应为空";
        }
        
        log.info("=== 单次查询性能测试完成 ===");
    }

    /**
     * 并发查询性能测试
     */
    @Test
    public void testConcurrentQueryPerformance() {
        log.info("=== 并发查询性能测试开始 ===");
        
        int concurrentUsers = 10; // 并发用户数
        int requestsPerUser = 5;  // 每用户请求数
        Integer provinceId = 1;   // 测试省份
        
        ExecutorService executor = Executors.newFixedThreadPool(concurrentUsers);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        long overallStartTime = System.currentTimeMillis();
        
        // 创建并发任务
        for (int i = 0; i < concurrentUsers; i++) {
            final int userId = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < requestsPerUser; j++) {
                    long startTime = System.currentTimeMillis();
                    try {
                        Result<DashboardSummaryDTO> result = powerDashboardController.getDashboard(provinceId);
                        long duration = System.currentTimeMillis() - startTime;
                        
                        log.debug("用户{}-请求{}: 耗时{}ms, 成功: {}", 
                                userId, j, duration, result.isSuccess());
                        
                        assert result.isSuccess() : "查询应该成功";
                        
                    } catch (Exception e) {
                        log.error("用户{}-请求{}失败: {}", userId, j, e.getMessage(), e);
                    }
                }
            }, executor);
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        long overallDuration = System.currentTimeMillis() - overallStartTime;
        int totalRequests = concurrentUsers * requestsPerUser;
        double avgResponseTime = (double) overallDuration / totalRequests;
        double throughput = (double) totalRequests / (overallDuration / 1000.0);
        
        log.info("并发测试完成 - 总请求数: {}, 总耗时: {}ms, 平均响应时间: {:.2f}ms, 吞吐量: {:.2f}req/s",
                totalRequests, overallDuration, avgResponseTime, throughput);
        
        executor.shutdown();
        log.info("=== 并发查询性能测试完成 ===");
    }

    /**
     * 全国汇总查询性能测试
     */
    @Test
    public void testNationalSummaryPerformance() {
        log.info("=== 全国汇总查询性能测试开始 ===");
        
        // 预热
        warmUp(0);
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        Result<DashboardSummaryDTO> result = powerDashboardController.getDashboard(0);
        long duration = System.currentTimeMillis() - startTime;
        
        log.info("全国汇总查询耗时: {}ms, 成功: {}", duration, result.isSuccess());
        
        // 验证结果
        assert result.isSuccess() : "全国汇总查询应该成功";
        assert result.getResult() != null : "结果不应为空";
        
        log.info("=== 全国汇总查询性能测试完成 ===");
    }

    /**
     * 压力测试
     */
    @Test
    public void testStressTest() {
        log.info("=== 压力测试开始 ===");
        
        int duration = 30; // 测试持续时间（秒）
        int concurrentUsers = 20; // 并发用户数
        Integer provinceId = 1;
        
        ExecutorService executor = Executors.newFixedThreadPool(concurrentUsers);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        long testStartTime = System.currentTimeMillis();
        long testEndTime = testStartTime + duration * 1000L;
        
        // 统计变量
        final int[] totalRequests = {0};
        final int[] successRequests = {0};
        final int[] errorRequests = {0};
        final long[] totalResponseTime = {0};
        
        // 创建压力测试任务
        for (int i = 0; i < concurrentUsers; i++) {
            final int userId = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                while (System.currentTimeMillis() < testEndTime) {
                    long requestStartTime = System.currentTimeMillis();
                    try {
                        Result<DashboardSummaryDTO> result = powerDashboardController.getDashboard(provinceId);
                        long requestDuration = System.currentTimeMillis() - requestStartTime;
                        
                        synchronized (totalRequests) {
                            totalRequests[0]++;
                            totalResponseTime[0] += requestDuration;
                            if (result.isSuccess()) {
                                successRequests[0]++;
                            } else {
                                errorRequests[0]++;
                            }
                        }
                        
                        // 短暂休息，避免过度压力
                        Thread.sleep(100);
                        
                    } catch (Exception e) {
                        synchronized (totalRequests) {
                            totalRequests[0]++;
                            errorRequests[0]++;
                        }
                        log.error("用户{}请求失败: {}", userId, e.getMessage());
                        
                        try {
                            Thread.sleep(500); // 错误后稍长休息
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }, executor);
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        long actualDuration = System.currentTimeMillis() - testStartTime;
        double avgResponseTime = totalRequests[0] > 0 ? (double) totalResponseTime[0] / totalRequests[0] : 0;
        double throughput = (double) totalRequests[0] / (actualDuration / 1000.0);
        double successRate = totalRequests[0] > 0 ? (double) successRequests[0] / totalRequests[0] * 100 : 0;
        
        log.info("压力测试完成:");
        log.info("  测试时长: {}秒", actualDuration / 1000);
        log.info("  总请求数: {}", totalRequests[0]);
        log.info("  成功请求: {}", successRequests[0]);
        log.info("  失败请求: {}", errorRequests[0]);
        log.info("  成功率: {:.2f}%", successRate);
        log.info("  平均响应时间: {:.2f}ms", avgResponseTime);
        log.info("  吞吐量: {:.2f}req/s", throughput);
        
        // 验证压力测试结果
        assert successRate >= 95.0 : "成功率应该大于95%";
        assert avgResponseTime <= 5000 : "平均响应时间应该小于5秒";
        
        executor.shutdown();
        log.info("=== 压力测试完成 ===");
    }

    /**
     * 预热方法
     */
    private void warmUp(Integer provinceId) {
        log.debug("预热开始 - 省份ID: {}", provinceId);
        try {
            // 执行几次预热请求
            for (int i = 0; i < 3; i++) {
                powerDashboardController.getDashboard(provinceId);
                Thread.sleep(100);
            }
        } catch (Exception e) {
            log.warn("预热过程中出现异常: {}", e.getMessage());
        }
        log.debug("预热完成 - 省份ID: {}", provinceId);
    }

    /**
     * 内存使用情况监控
     */
    @Test
    public void testMemoryUsage() {
        log.info("=== 内存使用情况测试开始 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存状态
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        log.info("初始内存使用: {} MB", initialMemory / 1024 / 1024);
        
        // 执行多次查询
        for (int i = 0; i < 100; i++) {
            powerDashboardController.getDashboard(1);
            
            if (i % 20 == 0) {
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                log.info("第{}次查询后内存使用: {} MB", i, currentMemory / 1024 / 1024);
            }
        }
        
        // 强制垃圾回收
        System.gc();
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        log.info("最终内存使用: {} MB", finalMemory / 1024 / 1024);
        log.info("内存增长: {} MB", (finalMemory - initialMemory) / 1024 / 1024);
        
        log.info("=== 内存使用情况测试完成 ===");
    }
}
