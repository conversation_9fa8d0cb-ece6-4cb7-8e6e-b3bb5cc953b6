package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.DashboardSummaryDTO;
import org.jeecg.modules.api.power_trade.dto.SettlementSummaryDTO;
import org.jeecg.modules.api.power_trade.dto.StationTypeStatisticsDTO;
import org.jeecg.modules.api.power_trade.entity.*;
import org.jeecg.modules.api.power_trade.enums.SettlementFileTypeEnum;
import org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper;
import org.jeecg.modules.api.power_trade.mapper.StationMapper;
import org.jeecg.modules.api.power_trade.service.*;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import static java.util.Optional.ofNullable;

/**
 * 电力交易首页概览数据服务实现类
 *
 * <AUTHOR> Agent
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PowerDashboardServiceImpl implements PowerDashboardService {

    private final StationService stationService;
    private final ScreenTradeSettlementMapper screenTradeSettlementMapper;
    private final MultiDataSourceAggregationService multiDataSourceAggregationService;
    private final PowerSideSettleService powerSideSettleService;
    private final PowerService powerService;
    private final IElectricityDataService electricityDataService;
    private final FileStationRelationService fileStationRelationService;
    private final YearlyPowerPlanService yearlyPowerPlanService;
    private final StationMapper stationMapper;

    @Autowired
    private ScreenTradeSettlementService screenTradeSettlementService;

    @Autowired
    private PowerDataService powerDataService;

    @Override
    public DashboardSummaryDTO getDashboardSummary(Integer provinceId) {
        log.info("开始获取首页概览数据 - 省份ID: {}", provinceId);

        try {
            DashboardSummaryDTO result = provinceId == 0
                    ? getNationalDashboardSummary()
                    : getSingleProvinceDashboardSummary(provinceId);

            ensureDashboardSummaryNotNull(result);
            log.info("首页概览数据获取完成 - 省份ID: {}", provinceId);
            return result;

        } catch (Exception e) {
            log.error("获取首页概览数据失败 - 省份ID: {}", provinceId, e);
            return createDefaultDashboardSummary();
        }
    }

    @Override
    public DashboardSummaryDTO getNationalDashboardSummary() {
        log.info("开始获取全国汇总首页概览数据");

        try {
            Map<String, Object> aggregatedData = multiDataSourceAggregationService
                    .aggregateAllProvincesDashboardSummary();
            return convertToDashboardSummaryDTO(aggregatedData);

        } catch (Exception e) {
            log.error("获取全国汇总首页概览数据失败", e);
            return createDefaultDashboardSummary();
        }
    }

    @Override
    public DashboardSummaryDTO getSingleProvinceDashboardSummary(Integer provinceId) {
        log.info("开始获取单省份首页概览数据 - 省份ID: {}", provinceId);

        return executeWithDataSource(provinceId, () -> {
            try {
                // 1. 获取电站数据
                List<Station> stationList = getTradeStations(provinceId);
                if (stationList.isEmpty()) {
                    log.info("省份{}无交易电站数据", provinceId);
                    return createDefaultDashboardSummary();
                }

                // 2. 计算电站统计数据
                StationStatistics stationStats = calculateStationStatistics(stationList);

                // 3. 获取结算数据
                SettlementData settlementData = getSettlementData(stationList, provinceId);

                // 4. 构建返回结果
                return buildDashboardSummary(stationStats, settlementData, provinceId);

            } catch (Exception e) {
                log.error("获取省份{}首页概览数据失败", provinceId, e);
                return createDefaultDashboardSummary();
            }
        });
    }

    /**
     * 获取参与交易的电站列表
     */
    private List<Station> getTradeStations(Integer provinceId) {
        LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
        stationQuery.eq(Station::getTradeStatus, 1)
                .eq(Station::getProvinceId, provinceId);
        return stationService.list(stationQuery);
    }

    /**
     * 计算电站统计数据
     */
    private StationStatistics calculateStationStatistics(List<Station> stationList) {
        // 计算交易容量
        double tradingCapacity = stationList.stream()
                .mapToDouble(Station::getCapacity)
                .sum();

        // 储能站功率单独累加
        double storagePower = stationList.stream()
                .filter(s -> s.getType() != null && s.getType() == 3)
                .mapToDouble(station -> ofNullable(station.getPower()).orElse(0.0))
                .sum();
        tradingCapacity += storagePower;

        // 统计各类型场站数量
        int windCount = (int) stationList.stream()
                .filter(s -> s.getType() != null && s.getType() == 1)
                .count();
        int solarCount = (int) stationList.stream()
                .filter(s -> s.getType() != null && s.getType() == 2)
                .count();
        int storageCount = (int) stationList.stream()
                .filter(s -> s.getType() != null && s.getType() == 3)
                .count();

        // 统计能源类型及其数量
        Map<Integer, Integer> energyTypeCountMap = stationList.stream()
                .filter(s -> s.getType() != null)
                .collect(Collectors.groupingBy(
                        Station::getType,
                        Collectors.summingInt(e -> 1)));

        return new StationStatistics(
                tradingCapacity, stationList.size(), windCount,
                solarCount, storageCount, energyTypeCountMap);
    }

    /**
     * 获取结算数据（统一使用PowerSideSettle数据源）
     */
    private SettlementData getSettlementData(List<Station> stationList, Integer provinceId) {
        try {
            List<Long> stationIds = stationList.stream()
                    .map(Station::getId)
                    .collect(Collectors.toList());

            log.info("查询首页概览数据（统一数据源），省份ID: {}, 电站数量: {}", provinceId, stationIds.size());

            // 使用统一的PowerSideSettle数据源，与交易概况接口保持一致
            Map<String, Object> summaryData = getUnifiedSettlementData(stationIds);

            if (!summaryData.isEmpty()) {
                log.info("查询到统一概览数据: {}", summaryData);
                return new SettlementData(
                        convertToDouble(summaryData.get("accumulatedPower")),
                        convertToDouble(summaryData.get("plannedPower")),
                        convertToDouble(summaryData.get("settlementAvgPrice")),
                        convertToDouble(summaryData.get("settlementPower")),
                        convertToDouble(summaryData.get("limitedPower")),
                        stationIds.size()
                );
            } else {
                log.warn("未查询到统一概览数据，使用默认值");
                return SettlementData.defaultData();
            }
        } catch (Exception e) {
            log.error("查询首页概览数据失败，使用默认值", e);
            return SettlementData.defaultData();
        }
    }

    /**
     * 构建首页概览数据
     */
    private DashboardSummaryDTO buildDashboardSummary(
            StationStatistics stationStats,
            SettlementData settlementData,
            Integer provinceId) {

        DashboardSummaryDTO result = new DashboardSummaryDTO();

        // 获取省份基准价格
        PriceConfig priceConfig = getPriceConfig(provinceId);

        result.setStationTypeStatistics(new StationTypeStatisticsDTO(
                formatToTwoDecimal(stationStats.getTradingCapacity()),
                stationStats.getStationCount(),
                stationStats.getWindCount(),
                stationStats.getSolarCount(),
                stationStats.getStorageCount(),
                formatToTwoDecimal(settlementData.getAccumulatedPower()),
                formatToTwoDecimal(settlementData.getPlannedPower()),
                formatToTwoDecimal(settlementData.getSettlementCount() == 0 ? 0.0 :
                        settlementData.getSettlementAvgPrice() / settlementData.getSettlementCount()),
                formatToTwoDecimal(settlementData.getSettlementPower()),
                formatToTwoDecimal(settlementData.getLimitedPower()),
                formatToTwoDecimal(priceConfig.getTargetPowerPrice()),
                formatToTwoDecimal(priceConfig.getBenchmarkPrice())));

        result.setEnergyTypeCount(stationStats.getEnergyTypeCountMap());

        return result;
    }

    /**
     * 数据源执行器
     */
    private <T> T executeWithDataSource(Integer provinceId, Supplier<T> operation) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            throw new IllegalArgumentException("不支持的省份ID: " + provinceId);
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            return operation.get();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 获取省份价格配置
     */
    private PriceConfig getPriceConfig(Integer provinceId) {
        switch (provinceId) {
            case 1:
                return new PriceConfig(391.0, 391.0);
            case 2:
                return new PriceConfig(384.4, 384.4);
            default:
                return new PriceConfig(0.0, 0.0);
        }
    }

    /**
     * 转换为Double类型
     */
    private double convertToDouble(Object value) {
        if (value == null) return 0.0;
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 转换聚合数据为DTO
     */
    private DashboardSummaryDTO convertToDashboardSummaryDTO(Map<String, Object> aggregatedData) {
        DashboardSummaryDTO result = new DashboardSummaryDTO();

        if (aggregatedData == null || aggregatedData.isEmpty()) {
            return createDefaultDashboardSummary();
        }

        try {
            // 创建电站类型统计数据（保留两位小数）
            StationTypeStatisticsDTO statistics = new StationTypeStatisticsDTO(
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("totalCapacity"))), // tradingCapacity
                    convertToInteger(aggregatedData.get("totalStations")), // stationCount
                    convertToInteger(aggregatedData.get("windStationCount")), // windStationCount
                    convertToInteger(aggregatedData.get("solarStationCount")), // solarStationCount
                    convertToInteger(aggregatedData.get("storageStationCount")), // storageStationCount
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("totalPowerGeneration"))), // accumulatedPower
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("plannedPower"))), // plannedPower
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("avgPrice"))), // settlementAvgPrice
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("settlementPower"))), // settlementPower
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("limitedPower"))), // limitedPower
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("targetPowerPrice"))), // targetPowerPrice
                    formatToTwoDecimal(convertToDouble(aggregatedData.get("benchmarkPrice"))) // benchmarkPrice
            );

            result.setStationTypeStatistics(statistics);

            // 设置能源类型统计（如果有的话）
            @SuppressWarnings("unchecked")
            Map<Integer, Integer> energyTypeCount = (Map<Integer, Integer>) aggregatedData.get("energyTypeCount");
            if (energyTypeCount != null) {
                result.setEnergyTypeCount(energyTypeCount);
            } else {
                result.setEnergyTypeCount(new HashMap<>());
            }

        } catch (Exception e) {
            log.error("转换聚合数据失败", e);
            return createDefaultDashboardSummary();
        }

        return result;
    }


    /**
     * 转换为Integer类型
     */
    private Integer convertToInteger(Object value) {
        if (value == null) return 0;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 确保返回值不为空
     */
    private void ensureDashboardSummaryNotNull(DashboardSummaryDTO dto) {
        if (dto == null) return;

        if (dto.getStationTypeStatistics() == null) {
            // 创建默认的电站类型统计数据，所有值为0
            dto.setStationTypeStatistics(new StationTypeStatisticsDTO(
                    formatToTwoDecimal(0.0), 0, 0, 0, 0,
                    formatToTwoDecimal(0.0), formatToTwoDecimal(0.0), formatToTwoDecimal(0.0),
                    formatToTwoDecimal(0.0), formatToTwoDecimal(0.0), formatToTwoDecimal(0.0), formatToTwoDecimal(0.0)
            ));
        }

        if (dto.getEnergyTypeCount() == null) {
            dto.setEnergyTypeCount(new HashMap<>());
        }
    }

    /**
     * 创建默认的首页概览数据
     */
    private DashboardSummaryDTO createDefaultDashboardSummary() {
        DashboardSummaryDTO result = new DashboardSummaryDTO();
        // 创建默认的电站类型统计数据，所有值为0
        result.setStationTypeStatistics(new StationTypeStatisticsDTO(
                formatToTwoDecimal(0.0), 0, 0, 0, 0,
                formatToTwoDecimal(0.0), formatToTwoDecimal(0.0), formatToTwoDecimal(0.0),
                formatToTwoDecimal(0.0), formatToTwoDecimal(0.0), formatToTwoDecimal(0.0), formatToTwoDecimal(0.0)
        ));
        result.setEnergyTypeCount(new HashMap<>());
        return result;
    }

    /**
     * 格式化数值为两位小数
     */
    private double formatToTwoDecimal(double value) {
        return Math.round(value * 100.0) / 100.0;
    }


    /**
     * 电站统计数据
     */
    @Getter
    private static class StationStatistics {
        // Getters
        private final double tradingCapacity;
        private final int stationCount;
        private final int windCount;
        private final int solarCount;
        private final int storageCount;
        private final Map<Integer, Integer> energyTypeCountMap;

        public StationStatistics(double tradingCapacity, int stationCount, int windCount,
                                 int solarCount, int storageCount, Map<Integer, Integer> energyTypeCountMap) {
            this.tradingCapacity = tradingCapacity;
            this.stationCount = stationCount;
            this.windCount = windCount;
            this.solarCount = solarCount;
            this.storageCount = storageCount;
            this.energyTypeCountMap = energyTypeCountMap;
        }

    }

    /**
     * 结算数据
     */
    @Getter
    private static class SettlementData {
        // Getters
        private final double accumulatedPower;
        private final double plannedPower;
        private final double settlementAvgPrice;
        private final double settlementPower;
        private final double limitedPower;
        private final int settlementCount;

        public SettlementData(double accumulatedPower, double plannedPower, double settlementAvgPrice,
                              double settlementPower, double limitedPower, int settlementCount) {
            this.accumulatedPower = accumulatedPower;
            this.plannedPower = plannedPower;
            this.settlementAvgPrice = settlementAvgPrice;
            this.settlementPower = settlementPower;
            this.limitedPower = limitedPower;
            this.settlementCount = settlementCount;
        }

        public static SettlementData defaultData() {
            return new SettlementData(0.0, 0.0, 0.0, 0.0, 0.0, 0);
        }

    }

    /**
     * 价格配置
     */
    @Getter
    private static class PriceConfig {
        private final double benchmarkPrice;
        private final double targetPowerPrice;

        public PriceConfig(double benchmarkPrice, double targetPowerPrice) {
            this.benchmarkPrice = benchmarkPrice;
            this.targetPowerPrice = targetPowerPrice;
        }

    }

    @Override
    public List<SettlementSummaryDTO> getSettlementSummary(Long stationId, Integer provinceId,
                                                           Integer dimension, String month, String year) {
        log.info("开始获取电站交易概况 - 省份ID: {}, 电站ID: {}, 维度: {}", provinceId, stationId, dimension);

        try {
            return provinceId == 0
                    ? getNationalSettlementSummary(stationId, dimension, month, year)
                    : getSingleProvinceSettlementSummary(stationId, provinceId, dimension, month, year);

        } catch (Exception e) {
            log.error("获取电站交易概况失败 - 省份ID: {}, 电站ID: {}", provinceId, stationId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SettlementSummaryDTO> getNationalSettlementSummary(Long stationId, Integer dimension,
                                                                   String month, String year) {
        log.info("开始获取全国汇总电站交易概况");

        try {
            return multiDataSourceAggregationService
                    .aggregateAllProvincesSettlementSummary(stationId, dimension, month, year);

        } catch (Exception e) {
            log.error("获取全国汇总电站交易概况失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SettlementSummaryDTO> getSingleProvinceSettlementSummary(Long stationId, Integer provinceId,
                                                                         Integer dimension, String month, String year) {
        log.info("开始获取单省份电站交易概况 - 省份ID: {}, 电站ID: {}", provinceId, stationId);

        return executeWithDataSource(provinceId, () -> {
            try {
                // 1. 获取目标电站列表
                List<Station> targetStations = getTargetStations(stationId, provinceId);
                if (targetStations.isEmpty()) {
                    log.warn("省份{}下没有找到电站数据", provinceId);
                    return new ArrayList<SettlementSummaryDTO>();
                }

                // 2. 计算时间范围
                TimeRange timeRange = calculateTimeRange(dimension, month, year);
                log.info("时间范围计算完成 - 开始日期: {}, 结束日期: {}, 开始年月: {}, 结束年月: {}",
                        timeRange.getStartDate(), timeRange.getEndDate(),
                        timeRange.getStartYearMonth(), timeRange.getEndYearMonth());

                // 3. 查询每个电站的结算数据
                List<SettlementSummaryDTO> resultList = new ArrayList<>();

                for (Station station : targetStations) {
                    try {
                        // 查询该电站的结算数据，基于power_side_settle表和file_station_relation表
                        Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                                station.getId(),
                                timeRange.getStartDate(),
                                timeRange.getEndDate(),
                                timeRange.getStartYearMonth(),
                                timeRange.getEndYearMonth()
                        );

                        // 构建DTO对象
                        SettlementSummaryDTO stationDto = new SettlementSummaryDTO();
                        stationDto.setStationId(station.getId());
                        stationDto.setStationName(station.getName());
                        stationDto.setStationType(station.getType());

                        // 从查询结果中获取数据
                        if (settlementData != null && !settlementData.isEmpty()) {
                            // 累计结算电量：settlement_electricity字段求和
                            BigDecimal totalSettlementElectricity = settlementData.getOrDefault("totalSettlementElectricity", BigDecimal.ZERO);

                            // 累计结算电费：settlement_electric_fee字段求和
                            BigDecimal totalSettlementElectricFee = settlementData.getOrDefault("totalSettlementElectricFee", BigDecimal.ZERO);

                            // 交易均价：settlement_electric_fee求和 ÷ settlement_electricity求和
                            BigDecimal avgTradePrice = settlementData.getOrDefault("avgTradePrice", BigDecimal.ZERO);

                            stationDto.setTotalSettlementElectricity(totalSettlementElectricity);
                            stationDto.setTotalSettlementElectricFee(totalSettlementElectricFee);
                            stationDto.setAvgTradePrice(avgTradePrice);

                            log.debug("电站结算数据查询成功 - 电站: {}, 累计结算电量: {} MWh, 交易均价: {} 元/MWh",
                                    station.getName(), totalSettlementElectricity, avgTradePrice);
                        } else {
                            // 无结算数据时设置为0
                            stationDto.setTotalSettlementElectricity(BigDecimal.ZERO);
                            stationDto.setTotalSettlementElectricFee(BigDecimal.ZERO);
                            stationDto.setAvgTradePrice(BigDecimal.ZERO);

                            log.debug("电站无结算数据 - 电站: {}", station.getName());
                        }

                        resultList.add(stationDto);

                    } catch (Exception e) {
                        log.error("查询电站{}结算数据失败", station.getName(), e);

                        // 异常时创建默认数据
                        SettlementSummaryDTO defaultDto = new SettlementSummaryDTO();
                        defaultDto.setStationId(station.getId());
                        defaultDto.setStationName(station.getName());
                        defaultDto.setStationType(station.getType());
                        defaultDto.setTotalSettlementElectricity(BigDecimal.ZERO);
                        defaultDto.setTotalSettlementElectricFee(BigDecimal.ZERO);
                        defaultDto.setAvgTradePrice(BigDecimal.ZERO);

                        resultList.add(defaultDto);
                    }
                }

                // 4. 按结算电量降序排列
                resultList.sort((a, b) -> b.getTotalSettlementElectricity().compareTo(a.getTotalSettlementElectricity()));

                log.info("单省份电站交易概况查询完成 - 省份ID: {}, 电站数: {}, 有数据电站数: {}",
                        provinceId, resultList.size(),
                        resultList.stream().mapToLong(dto -> dto.getTotalSettlementElectricity().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0).sum());

                return resultList;

            } catch (Exception e) {
                log.error("获取省份{}电站交易概况失败", provinceId, e);
                return new ArrayList<>();
            }
        });
    }

    /**
     * 获取目标电站列表
     */
    private List<Station> getTargetStations(Long stationId, Integer provinceId) {
        if (stationId != null) {
            // 查询指定电站
            LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
            stationQuery.eq(Station::getId, stationId)
                    .eq(Station::getProvinceId, provinceId);
            Station station = stationService.getOne(stationQuery);
            return station != null ? Collections.singletonList(station) : new ArrayList<>();
        } else {
            // 查询该省份的所有电站
            LambdaQueryWrapper<Station> allStationsQuery = new LambdaQueryWrapper<>();
            allStationsQuery.eq(Station::getProvinceId, provinceId);
            return stationService.list(allStationsQuery);
        }
    }

    /**
     * 计算时间范围
     */
    private TimeRange calculateTimeRange(Integer dimension, String month, String year) {
        if (dimension == 1) {
            // 月度查询
            String startDate = month + "-01";
            String endDate = getMonthEndDate(month);
            return new TimeRange(startDate, endDate, month, month);
        } else {
            // 年度查询
            String startDate = year + "-01-01";
            String endDate = year + "-12-31";
            String startYearMonth = year + "-01";
            String endYearMonth = year + "-12";
            return new TimeRange(startDate, endDate, startYearMonth, endYearMonth);
        }
    }

    /**
     * 处理单个电站的结算数据
     */
    private SettlementSummaryDTO processStationSettlement(Station station, TimeRange timeRange) {
        try {
            // 根据电站类型确定结算单类型
            SettlementFileTypeEnum fileType = station.getType() == 3
                    ? SettlementFileTypeEnum.SETTLE_MONTH_STORAGE_DETAILS
                    : SettlementFileTypeEnum.SETTLE_MONTH_DETAILS;

            // 查询单个电站的结算数据
            Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                    station.getId(), timeRange.getStartDate(), timeRange.getEndDate(),
                    timeRange.getStartYearMonth(), timeRange.getEndYearMonth());

            // 构造SettlementSummaryDTO
            BigDecimal totalElectricity = BigDecimal.ZERO;
            BigDecimal avgPrice = BigDecimal.ZERO;
            BigDecimal totalFee = BigDecimal.ZERO;

            if (settlementData != null && !settlementData.isEmpty()) {
                totalElectricity = Optional.ofNullable(settlementData.get("totalSettlementElectricity"))
                        .orElse(BigDecimal.ZERO);
                avgPrice = Optional.ofNullable(settlementData.get("avgTradePrice"))
                        .orElse(BigDecimal.ZERO);
                totalFee = Optional.ofNullable(settlementData.get("totalSettlementElectricFee"))
                        .orElse(BigDecimal.ZERO);
            }

            return new SettlementSummaryDTO(station.getId(), station.getName(), station.getType(),
                    totalElectricity, avgPrice, totalFee);

        } catch (Exception e) {
            log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
            // 即使查询失败，也返回基础信息
            return new SettlementSummaryDTO(station.getId(), station.getName(), station.getType(),
                    BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    /**
     * 获取月份的最后一天
     */
    private String getMonthEndDate(String month) {
        try {
            String[] parts = month.split("-");
            int year = Integer.parseInt(parts[0]);
            int monthNum = Integer.parseInt(parts[1]);

            java.time.YearMonth yearMonth = java.time.YearMonth.of(year, monthNum);
            int lastDay = yearMonth.lengthOfMonth();

            return String.format("%04d-%02d-%02d", year, monthNum, lastDay);
        } catch (Exception e) {
            log.warn("解析月份失败: {}", month, e);
            return month + "-31"; // 默认返回31号
        }
    }

    /**
     * 时间范围数据类
     */
    @Getter
    private static class TimeRange {
        private final String startDate;
        private final String endDate;
        private final String startYearMonth;
        private final String endYearMonth;

        public TimeRange(String startDate, String endDate, String startYearMonth, String endYearMonth) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.startYearMonth = startYearMonth;
            this.endYearMonth = endYearMonth;
        }

    }

    // ==================== Power Generation Trend 相关方法 ====================

    @Override
    public List<PowerGenerationTrendDto> getPowerGenerationTrend(PowerGenerationTrendQueryParam param) {
        log.info("开始获取发电趋势数据 - 省份ID: {}, 电站ID: {}", param.getProvinceId(), param.getStationId());

        try {
            // 参数验证
            String validationResult = validatePowerGenerationTrendParam(param);
            if (validationResult == null) {
                log.error("发电趋势参数验证失败");
                return new ArrayList<>();
            }

            return param.getProvinceId() == 0
                    ? getNationalPowerGenerationTrend(param)
                    : getSingleProvincePowerGenerationTrend(param);

        } catch (Exception e) {
            log.error("获取发电趋势数据失败 - 省份ID: {}, 电站ID: {}", param.getProvinceId(), param.getStationId(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PowerGenerationTrendDto> getNationalPowerGenerationTrend(PowerGenerationTrendQueryParam param) {
        log.info("开始获取全国汇总发电趋势数据");

        try {
            return multiDataSourceAggregationService.aggregateAllProvincesPowerGenerationTrend(param);
        } catch (Exception e) {
            log.error("获取全国汇总发电趋势数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PowerGenerationTrendDto> getSingleProvincePowerGenerationTrend(PowerGenerationTrendQueryParam param) {
        log.info("开始获取单省份发电趋势数据 - 省份ID: {}, 电站ID: {}", param.getProvinceId(), param.getStationId());

        return executeWithDataSource(param.getProvinceId(), () -> {
            try {
                // 参数验证
                String validationResult = validatePowerGenerationTrendParam(param);
                if (validationResult == null) {
                    return new ArrayList<PowerGenerationTrendDto>();
                }

                // 1. 获取目标电站列表
                List<Station> targetStations = getTargetStations(param.getStationId(), param.getProvinceId());
                if (targetStations.isEmpty()) {
                    log.warn("省份{}下没有找到电站数据", param.getProvinceId());
                    return new ArrayList<PowerGenerationTrendDto>();
                }

                List<PowerGenerationTrendDto> allTrendData = new ArrayList<>();

                // 2. 为每个电站查询发电趋势数据
                for (Station station : targetStations) {
                    try {
                        PowerGenerationTrendQueryParam stationParam = createStationParam(param, station.getId(), validationResult);

                        List<PowerGenerationTrendDto> stationTrendData = powerService.getPowerGenerationTrend(stationParam);

                        // 无论是否有数据，都确保返回完整的时间序列
                        List<PowerGenerationTrendDto> completeData = ensureCompleteTimeSeriesData(stationTrendData, param, station);

                        log.info("查询完成，电站ID: {}, 返回数据条数: {}", station.getId(), completeData.size());

                        allTrendData.addAll(completeData);

                    } catch (Exception e) {
                        log.error("查询电站{}发电趋势失败，创建完整的默认数据: {}", station.getId(), e.getMessage(), e);
                        // 即使查询失败，也创建完整的时间序列数据
                        List<PowerGenerationTrendDto> defaultData = createCompleteTimeSeriesData(param, station);
                        allTrendData.addAll(defaultData);
                    }
                }

                // 3. 按时间标签排序
                allTrendData.sort((a, b) -> {
                    if (a.getTimeLabel() == null && b.getTimeLabel() == null) return 0;
                    if (a.getTimeLabel() == null) return 1;
                    if (b.getTimeLabel() == null) return -1;
                    return a.getTimeLabel().compareTo(b.getTimeLabel());
                });

                log.info("单省份发电趋势查询完成 - 省份ID: {}, 总数据条数: {}", param.getProvinceId(), allTrendData.size());
                return allTrendData;

            } catch (Exception e) {
                log.error("获取省份{}发电趋势数据失败", param.getProvinceId(), e);
                return new ArrayList<>();
            }
        });
    }

    /**
     * 验证发电趋势查询参数
     */
    private String validatePowerGenerationTrendParam(PowerGenerationTrendQueryParam param) {
        if (param == null) {
            log.error("查询参数不能为空");
            return null;
        }

        if (param.getProvinceId() == null) {
            log.error("省份ID不能为空");
            return null;
        }

        if (param.getTimeDimension() == null || param.getTimeDimension().trim().isEmpty()) {
            log.error("时间维度不能为空");
            return null;
        }

        if (param.getQueryDate() == null || param.getQueryDate().trim().isEmpty()) {
            log.error("查询日期不能为空");
            return null;
        }

        // 转换时间维度
        String timeDimension = param.getTimeDimension().trim();
        switch (timeDimension) {
            case "年":
            case "year":
                return "1";
            case "月":
            case "month":
                return "2";
            case "日":
            case "day":
                return "3";
            default:
                if (timeDimension.matches("[123]")) {
                    return timeDimension;
                }
                log.error("不支持的时间维度: {}", timeDimension);
                return null;
        }
    }

    /**
     * 创建电站查询参数
     */
    private PowerGenerationTrendQueryParam createStationParam(PowerGenerationTrendQueryParam originalParam,
                                                              Long stationId, String timeDimension) {
        if (stationId == null) {
            throw new IllegalArgumentException("电站ID不能为空");
        }
        if (timeDimension == null || timeDimension.trim().isEmpty()) {
            throw new IllegalArgumentException("时间维度不能为空");
        }
        if (originalParam.getQueryDate() == null || originalParam.getQueryDate().trim().isEmpty()) {
            throw new IllegalArgumentException("查询日期不能为空");
        }

        PowerGenerationTrendQueryParam stationParam = new PowerGenerationTrendQueryParam();
        stationParam.setStationId(stationId);
        stationParam.setProvinceId(originalParam.getProvinceId());
        stationParam.setTimeDimension(timeDimension);
        stationParam.setQueryDate(originalParam.getQueryDate());
        return stationParam;
    }

    /**
     * 确保返回完整的时间序列数据（补齐缺失的时间点）
     */
    private List<PowerGenerationTrendDto> ensureCompleteTimeSeriesData(
            List<PowerGenerationTrendDto> actualData,
            PowerGenerationTrendQueryParam param,
            Station station) {

        // 创建完整的时间序列模板
        List<PowerGenerationTrendDto> completeData = createCompleteTimeSeriesData(param, station);

        if (actualData == null || actualData.isEmpty()) {
            return completeData;
        }

        // 将实际数据映射到完整的时间序列中
        Map<String, PowerGenerationTrendDto> actualDataMap = actualData.stream()
                .collect(Collectors.toMap(
                        PowerGenerationTrendDto::getTimeLabel,
                        dto -> dto,
                        (existing, replacement) -> replacement // 如果有重复，使用新的
                ));

        // 用实际数据替换默认数据
        for (PowerGenerationTrendDto template : completeData) {
            PowerGenerationTrendDto actual = actualDataMap.get(template.getTimeLabel());
            if (actual != null) {
                template.setPowerGeneration(actual.getPowerGeneration());
                template.setActualPower(actual.getActualPower());
            }
        }

        return completeData;
    }

    /**
     * 创建完整的时间序列数据模板
     */
    private List<PowerGenerationTrendDto> createCompleteTimeSeriesData(
            PowerGenerationTrendQueryParam param,
            Station station) {

        List<PowerGenerationTrendDto> completeData = new ArrayList<>();
        String timeDimension = param.getTimeDimension();

        if ("3".equals(timeDimension)) {
            // 日维度：创建96个点（每15分钟一个点）
            for (int i = 0; i < 96; i++) {
                int hour = i / 4;
                int minute = (i % 4) * 15;
                String timeLabel = String.format("%02d:%02d", hour, minute);

                PowerGenerationTrendDto trend = createTrendDto(timeLabel, station);
                completeData.add(trend);
            }

        } else if ("2".equals(timeDimension)) {
            // 月维度：创建该月所有天的数据
            String queryDate = param.getQueryDate(); // 格式：YYYY-MM
            int daysInMonth = getDaysInMonth(queryDate);

            for (int day = 1; day <= daysInMonth; day++) {
                String timeLabel = String.format("%02d日", day);
                PowerGenerationTrendDto trend = createTrendDto(timeLabel, station);
                completeData.add(trend);
            }

        } else if ("1".equals(timeDimension)) {
            // 年维度：创建12个月的数据
            for (int month = 1; month <= 12; month++) {
                String timeLabel = String.format("%02d月", month);
                PowerGenerationTrendDto trend = createTrendDto(timeLabel, station);
                completeData.add(trend);
            }
        }

        return completeData;
    }

    /**
     * 创建单个趋势数据点
     */
    private PowerGenerationTrendDto createTrendDto(String timeLabel, Station station) {
        PowerGenerationTrendDto trend = new PowerGenerationTrendDto();
        trend.setTimeLabel(timeLabel);
        trend.setPowerGeneration(0.0);
        trend.setActualPower(0.0);
        return trend;
    }

    /**
     * 获取指定月份的天数
     */
    private int getDaysInMonth(String yearMonth) {
        try {
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            java.time.YearMonth ym = java.time.YearMonth.of(year, month);
            return ym.lengthOfMonth();
        } catch (Exception e) {
            log.warn("解析年月失败: {}", yearMonth, e);
            return 31; // 默认返回31天
        }
    }

    @Override
    public Object getElectricityPriceData(Integer provinceId, Long stationId, String date, String dimension) {
        log.info("开始获取电量电价数据 - 省份ID: {}, 电站ID: {}, 日期: {}, 维度: {}",
                provinceId, stationId, date, dimension);

        try {
            // 参数验证
            if (!validateElectricityPriceParams(provinceId, stationId, date, dimension)) {
                throw new IllegalArgumentException("参数验证失败");
            }

            // 如果省份ID为0，根据电站ID获取省份ID
            Integer actualProvinceId = provinceId;
            if (provinceId == 0 && stationId != null) {
                actualProvinceId = getProvinceIdByStationId(stationId);
                if (actualProvinceId == null) {
                    throw new IllegalArgumentException("无法根据电站ID获取省份信息");
                }
                log.info("根据电站ID{}获取到省份ID: {}", stationId, actualProvinceId);
            }

            // 创建final变量供Lambda表达式使用
            final Integer finalProvinceId = actualProvinceId;
            final Long finalStationId = stationId;
            final String finalDate = date;
            final String finalDimension = dimension;

            // 使用数据源执行器查询数据
            return executeWithDataSource(finalProvinceId, () -> {
                try {
                    log.info("开始查询电量电价数据 - 实际省份ID: {}, 电站ID: {}, 日期: {}, 维度: {}",
                            finalProvinceId, finalStationId, finalDate, finalDimension);

                    Object result = electricityDataService.getElectricityDataByProvince(
                            finalProvinceId, finalStationId, finalDate, finalDimension);

                    log.info("电量电价数据查询完成 - 省份ID: {}, 结果类型: {}",
                            finalProvinceId, result != null ? result.getClass().getSimpleName() : "null");

                    return result;

                } catch (Exception e) {
                    log.error("查询电量电价数据失败 - 省份ID: {}, 电站ID: {}", finalProvinceId, finalStationId, e);
                    throw e;
                }
            });

        } catch (Exception e) {
            log.error("获取电量电价数据失败 - 省份ID: {}, 电站ID: {}", provinceId, stationId, e);
            throw e;
        }
    }

    @Override
    public Integer getProvinceIdByStationId(Long stationId) {
        if (stationId == null) {
            return null;
        }

        // 动态获取所有省份数据源
        Map<Integer, String> provinceDataSourceMap = ProvinceDataSourceUtil.getAllProvinceDataSource();
        List<Integer> sources = provinceDataSourceMap.keySet().stream()
                .filter(pid -> pid != 0)
                .sorted(Comparator.naturalOrder())
                .collect(Collectors.toList());

        log.info("开始在{}个省份中查找电站ID: {}, 省份列表: {}", sources.size(), stationId, sources);
        try {
            // 使用并行流在所有支持的省份中查找该电站，提升查询性能
            Optional<Integer> foundProvinceId = sources.parallelStream()
                    .map(provinceId -> {
                        try {
                            Station station = executeWithDataSource(provinceId, () -> {
                                LambdaQueryWrapper<Station> query = new LambdaQueryWrapper<>();
                                query.eq(Station::getId, stationId);
                                return stationService.getOne(query);
                            });

                            if (station != null) {
                                log.info("找到电站 - 电站ID: {}, 省份ID: {}, 电站名称: {}, 数据源: {}",
                                        stationId, provinceId, station.getName(), provinceDataSourceMap.get(provinceId));
                                return provinceId;
                            }
                        } catch (Exception e) {
                            log.debug("在省份{}中未找到电站{}: {}", provinceId, stationId, e.getMessage());
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)  // 过滤掉null值
                    .findFirst();  // 找到第一个匹配的省份ID

            if (foundProvinceId.isPresent()) {
                return foundProvinceId.get();
            }

            log.warn("未找到电站ID为{}的电站，已搜索省份: {}", stationId, sources);
            return null;

        } catch (Exception e) {
            log.error("根据电站ID获取省份ID失败 - 电站ID: {}", stationId, e);
            return null;
        }
    }

    /**
     * 验证电量电价查询参数
     */
    private boolean validateElectricityPriceParams(Integer provinceId, Long stationId, String date, String dimension) {
        if (provinceId == null) {
            log.error("省份ID不能为空");
            return false;
        }

        if (provinceId == 0 && stationId == null) {
            log.error("当省份ID为0时，电站ID不能为空");
            return false;
        }

        if (date == null || date.trim().isEmpty()) {
            log.error("日期不能为空");
            return false;
        }

        if (dimension == null || dimension.trim().isEmpty()) {
            log.error("维度不能为空");
            return false;
        }

        // 验证维度参数
        if (!Arrays.asList("1", "2", "3").contains(dimension.trim())) {
            log.error("不支持的维度参数: {}, 只支持 1(年)、2(月)、3(日)", dimension);
            return false;
        }

        // 验证日期格式
        try {
            if ("3".equals(dimension.trim())) {
                // 日维度：验证日期格式 yyyy-MM-dd
                if (!date.matches("\\d{4}-\\d{2}-\\d{2}")) {
                    log.error("日维度日期格式错误，应为 yyyy-MM-dd: {}", date);
                    return false;
                }
            } else if ("2".equals(dimension.trim())) {
                // 月维度：验证日期格式 yyyy-MM
                if (!date.matches("\\d{4}-\\d{2}")) {
                    log.error("月维度日期格式错误，应为 yyyy-MM: {}", date);
                    return false;
                }
            } else if ("1".equals(dimension.trim())) {
                // 年维度：验证日期格式 yyyy
                if (!date.matches("\\d{4}")) {
                    log.error("年维度日期格式错误，应为 yyyy: {}", date);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("日期格式验证失败: {}", e.getMessage());
            return false;
        }

        return true;
    }

    @Override
    public Map<String, Object> getPowerGenerationTrend(Integer provinceId, String dimension, Long stationId, String date) {
        log.info("开始获取发电趋势数据 - 省份ID: {}, 维度: {}, 场站ID: {}, 日期: {}",
                provinceId, dimension, stationId, date);

        try {
            // 构建PowerService查询参数
            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setTimeDimension(convertDimensionToCode(dimension));

            // 根据维度设置日期参数
            switch (dimension) {
                case "3":  // 日维度
                case "day":  // 兼容旧格式
                    param.setQueryDate(date);
                    break;
                case "2":  // 月维度
                case "month":  // 兼容旧格式
                    param.setQueryDate(date);
                    break;
                case "1":  // 年维度
                case "year":  // 兼容旧格式
                    param.setQueryDate(date);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的时间维度: " + dimension + "，支持：1-年度, 2-月度, 3-日度");
            }

            // 调用PowerService获取发电趋势数据
            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            // 获取场站信息
            Station station = stationService.getById(stationId);
            String stationName = station != null ? station.getName() : "未知场站";

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("dimension", dimension);
            result.put("stationId", stationId);
            result.put("stationName", stationName);
            result.put("timeRange", date);
            result.put("data", trendData);
            result.put("dataCount", trendData.size());

            log.info("发电趋势数据获取成功 - 场站: {}, 维度: {}, 数据点数: {}",
                    stationName, dimension, trendData.size());

            return result;

        } catch (Exception e) {
            log.error("获取发电趋势数据失败 - 场站ID: {}, 维度: {}, 日期: {}",
                    stationId, dimension, date, e);
            throw new RuntimeException("获取发电趋势数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换维度参数为PowerService需要的代码
     * 支持新的标准化编码：1-年度, 2-月度, 3-日度
     */
    private String convertDimensionToCode(String dimension) {
        // 支持新的数字编码格式
        switch (dimension) {
            case "1":
                return "1";  // 年维度
            case "2":
                return "2";  // 月维度
            case "3":
                return "3";  // 日维度
            // 兼容旧的字符串格式（向后兼容）
            case "year":
                return "1";  // 年维度
            case "month":
                return "2";  // 月维度
            case "day":
                return "3";  // 日维度
            default:
                throw new IllegalArgumentException("不支持的时间维度: " + dimension + "，支持：1-年度, 2-月度, 3-日度");
        }
    }

    /**
     * 获取统一的结算数据（使用PowerService获取真实发电量）
     * 发电量数据使用PowerService（功率预测），其他数据保持原有逻辑
     */
    private Map<String, Object> getUnifiedSettlementData(List<Long> stationIds) {
        // 定义返回结果 Map
        Map<String, Object> result = new HashMap<>();

        // 常量定义（避免魔法值）
        final String KEY_ACCUMULATED_POWER = "accumulatedPower";
        final String KEY_PLANNED_POWER = "plannedPower";
        final String KEY_SETTLEMENT_AVG_PRICE = "settlementAvgPrice";
        final String KEY_SETTLEMENT_POWER = "settlementPower";
        final String KEY_LIMITED_POWER = "limitedPower";
        final String KEY_RECORD_COUNT = "recordCount";

        try {
            log.info("开始获取统一结算数据，电站数量: {}", stationIds.size());

            // ========== 1. 初始化汇总变量 ==========
            BigDecimal totalGeneration = BigDecimal.ZERO;               // 累计发电量（来自 PowerService）
            BigDecimal totalSettlementElectricity = BigDecimal.ZERO;    // 结算电量（来自 PowerSideSettle）
            BigDecimal totalSettlementElectricFee = BigDecimal.ZERO;    // 结算电费
            BigDecimal limitedPower = BigDecimal.ZERO;                  // 限电量
            BigDecimal planPower = BigDecimal.ZERO;                     // 计划发电量
            int totalRecordCount = 0;                                   // 成功获取发电数据的电站数量

            String currentYear = String.valueOf(java.time.Year.now().getValue()); // 当前年份，如 "2025"

            LocalDate firstDayOfYear = LocalDate.of(Integer.parseInt(currentYear), 1, 1);
            LocalDate lastDayOfYear = firstDayOfYear.with(TemporalAdjusters.lastDayOfYear());

            // ========== 2. 1.1 获取累计发电量（通过 PowerService，按电站循环） ==========
            if (!stationIds.isEmpty()) {
                for (Long stationId : stationIds) {
                    try {
                        PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
                        param.setStationId(stationId);
                        param.setTimeDimension("1"); // 年维度
                        param.setQueryDate(currentYear);

                        List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

                        double stationGeneration = trendData.stream()
                                .map(PowerGenerationTrendDto::getPowerGeneration)
                                .filter(Objects::nonNull)
                                .mapToDouble(Double::doubleValue)
                                .sum();

                        totalGeneration = totalGeneration.add(BigDecimal.valueOf(stationGeneration));
                        totalRecordCount++;

                    } catch (Exception e) {
                        log.warn("获取电站 {} 发电量数据失败: {}", stationId, e.getMessage(), e);
                    }
                }
            } else {
                log.warn("传入的电站ID列表为空，跳过发电量数据获取");
            }

            // ========== 2.2 获取结算相关数据：fileStationRelation -> fileIds -> powerSideSettle ==========
            List<PowerSideSettle> powerSideSettles = Collections.emptyList();
            if (!stationIds.isEmpty()) {
                // Step 1: 查询符合条件的 FileStationRelation 记录
                List<FileStationRelation> fileStationRelations = fileStationRelationService.list(
                        new LambdaQueryWrapper<FileStationRelation>()
                                .in(FileStationRelation::getStationId, stationIds)
                                .ge(FileStationRelation::getSettleDate, firstDayOfYear)
                                .le(FileStationRelation::getSettleDate, lastDayOfYear)
                );

                // Step 2: 提取 fileId 列表，如果为空则不查询 powerSideSettle
                List<Long> fileIds = fileStationRelations.stream()
                        .map(FileStationRelation::getId)
                        .collect(Collectors.toList());

                if (!fileIds.isEmpty()) {
                    powerSideSettles = powerSideSettleService.list(
                            new LambdaQueryWrapper<PowerSideSettle>()
                                    .in(PowerSideSettle::getFileId, fileIds)
                    );
                } else {
                    log.info("未查询到有效的 FileStationRelation 数据，跳过结算电量与电费统计");
                }
            } else {
                log.warn("电站ID列表为空，跳过结算数据查询");
            }

            // Step 3: 汇总结算电量与电费
            totalSettlementElectricity = powerSideSettles.stream()
                    .map(PowerSideSettle::getSettlementElectricity)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            totalSettlementElectricFee = powerSideSettles.stream()
                    .map(PowerSideSettle::getSettlementElectricFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // ========== 2.3 获取限电量数据（ScreenTradeSettlement） ==========
            if (!stationIds.isEmpty()) {
                LambdaQueryWrapper<ScreenTradeSettlement> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(ScreenTradeSettlement::getStationId, stationIds)
                        .eq(ScreenTradeSettlement::getYear, currentYear);

                List<ScreenTradeSettlement> allSettlements = screenTradeSettlementService.list(wrapper);

                for (ScreenTradeSettlement settlement : allSettlements) {
                    if (settlement.getLimitedPower() != null) {
                        limitedPower = limitedPower.add(settlement.getLimitedPower());
                    }
                }
            } else {
                log.warn("电站ID列表为空，跳过限电量数据查询");
            }

            // ========== 2.4 获取计划发电量（YearlyPowerPlan） ==========
            if (!stationIds.isEmpty()) {
                LambdaQueryWrapper<YearlyPowerPlan> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(YearlyPowerPlan::getYear, currentYear)
                        .in(YearlyPowerPlan::getStationId, stationIds);

                List<YearlyPowerPlan> allPlans = yearlyPowerPlanService.list(wrapper);

                for (YearlyPowerPlan plan : allPlans) {
                    if (plan.getPlanValue() != null) {
                        planPower = planPower.add(plan.getPlanValue());
                    }
                }
            } else {
                log.warn("电站ID列表为空，跳过计划发电量数据查询");
            }

            // ========== 2.5 计算交易均价 ==========
            BigDecimal avgPrice = BigDecimal.ZERO;
            if (totalSettlementElectricity.compareTo(BigDecimal.ZERO) > 0 && totalSettlementElectricFee.compareTo(BigDecimal.ZERO) > 0) {
                avgPrice = totalSettlementElectricFee.divide(totalSettlementElectricity, 6, RoundingMode.HALF_UP);
            }

            // ========== 3. 构建返回结果 ==========
            result.put(KEY_ACCUMULATED_POWER, totalGeneration.doubleValue());         // 发电量（double）
            result.put(KEY_PLANNED_POWER, planPower);                                  // 计划电量
            result.put(KEY_SETTLEMENT_AVG_PRICE, avgPrice.doubleValue());              // 均价（double）
            result.put(KEY_SETTLEMENT_POWER, totalSettlementElectricity);             // 结算电量
            result.put(KEY_LIMITED_POWER, limitedPower);                               // 限电量
            result.put(KEY_RECORD_COUNT, totalRecordCount);                            // 成功获取数据的电站数

            log.info("统一结算数据获取完成，电站数量：{}，成功获取发电数据的电站：{}", stationIds.size(), totalRecordCount);

            return result;

        } catch (Exception e) {
            log.error("获取统一结算数据发生异常", e);
            // 返回空 Map 或者考虑返回带有错误标识的 Map / 统一响应对象
            return new HashMap<>();
        }
    }

    /**
     * 获取电站计划发电量
     */
    private BigDecimal getStationPlanPower(Long stationId) {
        try {
            // 获取当前年份的计划发电量
            String currentYear = String.valueOf(java.time.Year.now().getValue());

            com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<YearlyPowerPlan> queryWrapper =
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
            queryWrapper.eq(YearlyPowerPlan::getStationId, stationId)
                    .eq(YearlyPowerPlan::getYear, currentYear);

            List<YearlyPowerPlan> planList = yearlyPowerPlanService.list(queryWrapper);

            return planList.stream()
                    .map(YearlyPowerPlan::getPlanValue)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.warn("获取电站{}计划发电量失败: {}", stationId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 使用PowerService获取电站累计发电量
     * 这是真实的发电量数据，来自功率预测系统
     */
    private BigDecimal getStationTotalGenerationFromPowerService(Long stationId, String year) {
        try {
            log.debug("使用PowerService获取电站{}年度{}累计发电量", stationId, year);

            // 构建PowerService查询参数
            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setTimeDimension("1"); // 年维度
            param.setQueryDate(year);

            // 调用PowerService获取发电趋势数据
            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            if (trendData == null || trendData.isEmpty()) {
                log.warn("电站{}年度{}无发电量数据", stationId, year);
                return BigDecimal.ZERO;
            }

            // 聚合所有月份的发电量
            double totalGeneration = trendData.stream()
                    .map(PowerGenerationTrendDto::getPowerGeneration)
                    .filter(Objects::nonNull)
                    .reduce(0.0, Double::sum);

            BigDecimal result = BigDecimal.valueOf(totalGeneration);
            log.debug("电站{}年度{}累计发电量: {} MWh", stationId, year, result);

            return result;

        } catch (Exception e) {
            log.error("使用PowerService获取电站{}累计发电量失败: {}", stationId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取电站结算电费（用于计算交易均价）
     */
    private BigDecimal getStationSettlementElectricFee(Long stationId, String year) {
        try {
            log.debug("获取电站{}年度{}结算电费", stationId, year);

            // 1. 查询全年的 FileStationRelation 数据
            LocalDate firstDayOfYear = LocalDate.of(Integer.parseInt(year), 1, 1);
            LocalDate lastDayOfYear = firstDayOfYear.with(TemporalAdjusters.lastDayOfYear());

            List<FileStationRelation> fileStationRelations = fileStationRelationService.list(
                    new LambdaQueryWrapper<FileStationRelation>()
                            .eq(FileStationRelation::getStationId, stationId)
                            .ge(FileStationRelation::getSettleDate, firstDayOfYear)
                            .le(FileStationRelation::getSettleDate, lastDayOfYear)
            );

            if (fileStationRelations.isEmpty()) {
                log.debug("电站{}年度{}无FileStationRelation数据", stationId, year);
                return BigDecimal.ZERO;
            }

            // 2. 查询对应的 PowerSideSettle 数据
            List<Long> fileIds = fileStationRelations.stream()
                    .map(FileStationRelation::getId)
                    .collect(Collectors.toList());

            List<PowerSideSettle> powerSideSettles = powerSideSettleService.list(
                    new LambdaQueryWrapper<PowerSideSettle>()
                            .in(PowerSideSettle::getFileId, fileIds)
            );

            // 3. 汇总结算电费
            BigDecimal totalSettlementElectricFee = powerSideSettles.stream()
                    .map(PowerSideSettle::getSettlementElectricFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("电站{}年度{}结算电费: {} 元", stationId, year, totalSettlementElectricFee);
            return totalSettlementElectricFee;

        } catch (Exception e) {
            log.error("获取电站{}结算电费失败: {}", stationId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }


    /**
     * 原有的buildYearlyTradingDataList逻辑已被PowerService替代
     * 现在使用PowerService获取真实的功率预测发电量数据
     */
    @Deprecated
    private List<Map<String, Object>> buildUnifiedYearlyTradingDataListForDashboard_DEPRECATED(Station station, String year) {
        List<Map<String, Object>> yearlyDataList = new ArrayList<>();

        try {
            // 1. 查询全年的 YearlyPowerPlan 数据（按月份分组）
            List<YearlyPowerPlan> yearlyPowerPlans = yearlyPowerPlanService.list(
                    new LambdaQueryWrapper<YearlyPowerPlan>()
                            .eq(YearlyPowerPlan::getStationId, station.getId())
                            .eq(YearlyPowerPlan::getYear, year)
            );

            // 按月份分组存储计划电量
            Map<Integer, BigDecimal> monthlyPlanPowerMap = yearlyPowerPlans.stream()
                    .collect(Collectors.groupingBy(
                            plan -> Integer.parseInt(plan.getMonth()), // 月份（1-12）
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    YearlyPowerPlan::getPlanValue,
                                    BigDecimal::add
                            )
                    ));

            // 2. 查询全年的 FileStationRelation 数据（筛选该年范围内的记录）
            LocalDate firstDayOfYear = LocalDate.of(Integer.parseInt(year), 1, 1);
            LocalDate lastDayOfYear = firstDayOfYear.with(TemporalAdjusters.lastDayOfYear());

            List<FileStationRelation> fileStationRelations = fileStationRelationService.list(
                    new LambdaQueryWrapper<FileStationRelation>()
                            .eq(FileStationRelation::getStationId, station.getId())
                            .ge(FileStationRelation::getSettleDate, firstDayOfYear)
                            .le(FileStationRelation::getSettleDate, lastDayOfYear)
            );

            // 按月份分组存储 file_id
            Map<Integer, List<Long>> monthlyFileIdsMap = fileStationRelations.stream()
                    .collect(Collectors.groupingBy(
                            relation -> relation.getSettleDate().getMonthValue(), // 1-12
                            Collectors.mapping(FileStationRelation::getId, Collectors.toList())
                    ));

            // 3. 查询全年的 PowerSideSettle 数据（筛选该年范围内的记录）
            List<Long> allFileIds = fileStationRelations.stream()
                    .map(FileStationRelation::getId)
                    .collect(Collectors.toList());

            List<PowerSideSettle> powerSideSettles = new ArrayList<>();
            if (!allFileIds.isEmpty()) {
                powerSideSettles = powerSideSettleService.list(
                        new LambdaQueryWrapper<PowerSideSettle>()
                                .in(PowerSideSettle::getFileId, allFileIds)
                );
            }

            // 按月份分组存储结算数据
            Map<Integer, List<PowerSideSettle>> monthlyPowerSideSettlesMap = powerSideSettles.stream()
                    .collect(Collectors.groupingBy(
                            settle -> settle.getFileId() != null ?
                                    fileStationRelations.stream()
                                            .filter(relation -> relation.getId().equals(settle.getFileId()))
                                            .findFirst()
                                            .map(FileStationRelation::getSettleDate)
                                            .map(LocalDate::getMonthValue)
                                            .orElse(0) : 0
                    ));

            // 4. 遍历1-12月，构建每月数据
            for (int monthNum = 1; monthNum <= 12; monthNum++) {
                Map<String, Object> monthData = new HashMap<>();
                monthData.put("year", year);
                monthData.put("month", String.format("%02d", monthNum));

                // 4.1 获取计划电量（来自 YearlyPowerPlan）
                BigDecimal monthlyPlanPower = monthlyPlanPowerMap.getOrDefault(monthNum, BigDecimal.ZERO);
                monthData.put("current_month_power", monthlyPlanPower);

                // 4.2 获取结算电量和电费（来自 PowerSideSettle）
                List<PowerSideSettle> monthlyPowerSideSettles = monthlyPowerSideSettlesMap.getOrDefault(monthNum, new ArrayList<>());
                BigDecimal totalSettlementElectricity = monthlyPowerSideSettles.stream()
                        .map(PowerSideSettle::getSettlementElectricity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalSettlementElectricFee = monthlyPowerSideSettles.stream()
                        .map(PowerSideSettle::getSettlementElectricFee)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal averagePrice = totalSettlementElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                        totalSettlementElectricFee.divide(totalSettlementElectricity, 6, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;

                monthData.put("totalSettlementElectricity", totalSettlementElectricity);
                monthData.put("averagePrice", averagePrice);
                monthData.put("totalSettlementElectricFee", totalSettlementElectricFee);
                monthData.put("settlementRecordCount", monthlyPowerSideSettles.size());

                yearlyDataList.add(monthData);
            }

            log.debug("电站{}年度数据构建完成 - 共{}个月数据", station.getId(), yearlyDataList.size());

        } catch (Exception e) {
            log.error("构建电站{}年度交易数据失败", station.getId(), e);
        }
        return yearlyDataList;
    }
}
