package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.RolloverProfit;

import java.util.List;

/**
 * 滚撮收益服务接口
 */
public interface RolloverProfitService extends IService<RolloverProfit> {

    /**
     * 根据电站ID和日期查询滚撮收益数据
     */
    List<RolloverProfit> selectByStationAndDate(Long stationId, String targetDate);

    /**
     * 根据电站ID和月份查询滚撮收益数据
     */
    List<RolloverProfit> selectByStationAndMonth(Long stationId, String yearMonth);

    /**
     * 根据电站ID和年份查询滚撮收益数据
     */
    List<RolloverProfit> selectByStationAndYear(Long stationId, String year);
}
