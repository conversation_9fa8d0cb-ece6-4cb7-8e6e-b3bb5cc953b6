package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.mapper.EnergyStorageDailyCleanMapper;
import org.jeecg.modules.api.power_trade.service.EnergyStorageDailyCleanService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
public class EnergyStorageDailyCleanServiceImpl extends ServiceImpl<EnergyStorageDailyCleanMapper, EnergyStorageDailyClean> implements EnergyStorageDailyCleanService {
    
    @Override
    public List<EnergyStorageDailyClean> selectByDay(String stationId, String date) {
        return baseMapper.selectByDay(stationId, date);
    }
    
    @Override
    public List<EnergyStorageDailyClean> selectByMonth(Long stationId, String date) {
        try {
            log.info("开始查询储能日清分月度数据 - 电站ID: {}, 日期: {}", stationId, date);

            // 解析日期，获取年月信息
            YearMonth yearMonth;
            try {
                if (date.length() == 7) { // 格式：2025-01
                    yearMonth = YearMonth.parse(date);
                } else if (date.length() == 10) { // 格式：2025-01-15
                    LocalDate localDate = LocalDate.parse(date);
                    yearMonth = YearMonth.from(localDate);
                } else {
                    throw new IllegalArgumentException("日期格式不正确，应为 yyyy-MM 或 yyyy-MM-dd");
                }
            } catch (Exception e) {
                log.error("日期解析失败: {}", date, e);
                return new EnergyStorageDailyClean();
            }

            // 获取月份的第一天和最后一天
            LocalDate firstDay = yearMonth.atDay(1);
            LocalDate lastDay = yearMonth.atEndOfMonth();

            log.debug("查询储能日清分数据 - 电站ID: {}, 开始日期: {}, 结束日期: {}",
                    stationId, firstDay, lastDay);

            // 调用Mapper查询月度数据
            List<EnergyStorageDailyClean> result = baseMapper.selectByMonth(stationId.toString(), date);

            if (result == null) {
                result = new ArrayList<>();
            }

            log.info("储能日清分月度数据查询完成 - 电站ID: {}, 日期: {}, 结果数量: {}",
                    stationId, date, result.size());

            return result;

        } catch (Exception e) {
            log.error("查询储能日清分月度数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EnergyStorageDailyClean> selectByYear(String stationId, String date) {
        return baseMapper.selectByYear(stationId, date);
    }

    /**
     * 创建默认的EnergyStorageDailyClean记录（所有数值字段为0）
     */
    private EnergyStorageDailyClean createDefaultEnergyStorageDailyClean(Long stationId, LocalDate localDate) {
        EnergyStorageDailyClean defaultData = new EnergyStorageDailyClean();
        defaultData.setStationId(stationId);

        // 将LocalDate转换为Date
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        defaultData.setDate(date);

        // 设置所有数值字段为0
        defaultData.setUserDayAheadDeviationPower(BigDecimal.ZERO);
        defaultData.setUserDayAheadDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setUserDayAheadDeviationFee(BigDecimal.ZERO);
        defaultData.setUserRealtimeDeviationPower(BigDecimal.ZERO);
        defaultData.setUserRealtimeDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setUserRealtimeDeviationFee(BigDecimal.ZERO);
        defaultData.setUserTotalPower(BigDecimal.ZERO);
        defaultData.setUserTotalFee(BigDecimal.ZERO);
        defaultData.setPowerGenerationDayAheadDeviationPower(BigDecimal.ZERO);
        defaultData.setPowerGenerationDayAheadDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setPowerGenerationDayAheadDeviationFee(BigDecimal.ZERO);
        defaultData.setPowerGenerationRealtimeDeviationPower(BigDecimal.ZERO);
        defaultData.setPowerGenerationRealtimeDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setPowerGenerationRealtimeDeviationFee(BigDecimal.ZERO);
        defaultData.setPowerGenerationTotalPower(BigDecimal.ZERO);
        defaultData.setPowerGenerationTotalFee(BigDecimal.ZERO);

        defaultData.setCreateTime(new Date());
        defaultData.setUpdateTime(new Date());
        return defaultData;
    }
}