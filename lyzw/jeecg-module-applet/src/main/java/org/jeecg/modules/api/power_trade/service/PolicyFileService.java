package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.PolicyFile;
import org.jeecg.modules.api.power_trade.vo.PolicyFileVO;
import org.springframework.stereotype.Service;

@Service
public interface PolicyFileService extends IService<PolicyFile> {

    /**
     * 获取政策文件列表
     *
     * @param page 分页参数
     * @param provinceId 省份ID
     * @param keyword 关键词
     * @return 政策文件列表
     */
    IPage<PolicyFileVO> getPolicyFileList(Page<PolicyFile> page, Integer provinceId, String keyword);
}
