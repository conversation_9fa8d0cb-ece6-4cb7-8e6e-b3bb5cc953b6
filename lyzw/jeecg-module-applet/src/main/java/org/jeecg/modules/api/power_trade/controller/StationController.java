package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.realm.CachingRealm;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.EnergyNewDailyCleanWithRolloverDTO;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.enums.TimeQueryType;
import org.jeecg.modules.api.power_trade.service.*;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 电力交易电站控制器
 */
@Slf4j
@RestController
@Api(tags = "电力交易-电站接口")
@RequestMapping("/api/power_trade/stations")
public class StationController {

    @Autowired
    private StationService stationService;

    @Autowired
    private ScreenTradeSettlementService screenTradeSettlementService;

    @Autowired
    private DayAheadClearPowerService dayAheadClearPowerService;

    @Autowired
    private EnergyNewDailyCleanService energyNewDailyCleanService;

    @Autowired
    private EnergyStorageDailyCleanService energyStorageDailyCleanService;

    @Autowired
    private YearlyPowerPlanService yearlyPowerPlanService;

    @Autowired
    private RolloverProfitService rolloverProfitService;

    @Autowired
    private PowerService powerService;

    @Autowired
    private PowerSideSettleService powerSideSettleService;

    @Autowired
    private MultiDataSourceAggregationService multiDataSourceAggregationService;

    @Autowired
    private StationBusinessService stationBusinessService;

    @Autowired
    private CachingRealm cachingRealm;

    @GetMapping("/list")
    @ApiOperation(value = "电站交易总览", notes = "根据省份显示涉及交易场站列表，包含累计结算电量和交易均价")
    public Result<Map<String, Object>> getStationList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "省份ID (0-全国, 1-安徽, 2-江苏)", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "查询维度 (1-月度, 2-年度)", required = true) @RequestParam Integer dimension,
            @ApiParam(value = "年份 (默认当前年)") @RequestParam(required = false) String year,
            @ApiParam(value = "月份 (维度为月度时必填，格式：01-12，默认当前月)") @RequestParam(required = false) String month,
            @ApiParam(value = "电站名称搜索") @RequestParam(required = false) String name) {

        // 参数验证和默认值处理
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create()
                    .validatePagination(pageNo, pageSize)
                    .validateProvinceId(provinceId)
                    .validateQueryDimension(dimension);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        try {
            // 设置默认值
            if (year == null || year.trim().isEmpty()) {
                year = String.valueOf(java.time.LocalDate.now().getYear());
            }

            if (dimension == 1) { // 月度查询
                if (month == null || month.trim().isEmpty()) {
                    month = String.format("%02d", java.time.LocalDate.now().getMonthValue());
                }
                // 验证月份格式
                if (!month.matches("^(0[1-9]|1[0-2])$")) {
                    return Result.error("月份格式错误，应为：01-12");
                }
            } else { // 年度查询
                month = null; // 年度查询不需要月份
            }

            String safeName = null;
            if (name != null && !name.trim().isEmpty()) {
                try {
                    safeName = URLDecoder.decode(name, StandardCharsets.UTF_8.name());
                } catch (UnsupportedEncodingException e) {
                    log.warn("URL解码失败，使用原始name参数: {}", name, e);
                    safeName = name;
                }
            }

            Map<String, Object> result = stationBusinessService.getStationTradingOverview(
                    pageNo, pageSize, provinceId, dimension, year, month, safeName);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取电站交易概况失败 - 省份ID: {}, 维度: {}", provinceId, dimension, e);
            return Result.error("获取电站交易概况失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "电站详情", notes = "获取电站详细信息，包括基础信息、发电量和交易信息")
    @GetMapping("/{id}/detail")
    public Result<StationDetailResponseDTO> getStationDetail(
            @PathVariable Long id,
            @RequestParam String provinceId,
            @RequestParam String date,
            @RequestParam(defaultValue = "1") String dimension) {
        try {
            // 参数验证
            Result<String> validationResult = ParamValidationUtil.validate(() -> {
                ParamValidationUtil.Validator validator = ParamValidationUtil.Validator.create();
                validator.validateStationId(id);
                String timeDimension = validator.validateTimeDimension(dimension);
                validator.validateDateFormat(date, timeDimension);
                return timeDimension;
            });
            if (!validationResult.isSuccess()) {
                return Result.error(validationResult.getMessage());
            }
            String timeDimension = validationResult.getResult();

            StationDetailResponseDTO response = stationBusinessService.getStationDetail(
                    id, provinceId, date, timeDimension);
            return Result.OK(response);

        } catch (NumberFormatException e) {
            log.error("省份ID格式错误: {}", provinceId, e);
            return Result.error("省份ID格式错误");
        } catch (Exception e) {
            log.error("获取电站详情失败 - 电站ID: {}, 省份ID: {}", id, provinceId, e);
            return Result.error("获取电站详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取电站年度交易电量信息
     */
    @GetMapping("/{id}/yearly-trading-info")
    @ApiOperation(value = "获取电站年度交易电量信息", notes = "获取电站的年度交易信息，包含月度分解数据")
    public Result<Map<String, Object>> getStationYearlyTradingInfo(
            @ApiParam(value = "电站ID", required = true) @PathVariable Long id,
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "年份", required = true) @RequestParam String year) {
        try {
            // 参数验证
            if (id == null || id <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (year == null || !year.matches("\\d{4}")) {
                return Result.error("年份格式不正确，请使用yyyy格式");
            }
            Map<String, Object> result = new HashMap<>();

            // 切换到对应省份的数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID");
            }
            DynamicDataSourceContextHolder.push(dsKey);

            log.info("查询电站年度交易电量信息 - 电站ID: {}, 省份ID: {}, 年份: {}", id, provinceId, year);

            // 验证电站是否属于该省份
            Station station = stationService.getById(id);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            // 查询年度交易电量信息
            Map<String, Object> tradingInfo = powerSideSettleService.getStationYearlyTradingInfo(id, year);

            // 添加额外信息
            result.put("stationInfo", station);
            result.put("tradingInfo", tradingInfo);

            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取电站年度交易电量信息失败 - 电站ID: {}, 年份: {}", id, year, e);
            return Result.error("获取年度交易电量信息失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    // 解析时间查询类型的工具方法
    private TimeQueryType parseTimeQueryType(String dateParam) {
        if (dateParam.matches("\\d{4}-\\d{2}-\\d{2}")) {
            // yyyy-MM-dd 格式 - 日查询
            return TimeQueryType.DAY;
        } else if (dateParam.matches("\\d{4}-\\d{2}")) {
            // yyyy-MM 格式 - 月查询
            return TimeQueryType.MONTH;
        } else if (dateParam.matches("\\d{4}")) {
            // yyyy 格式 - 年查询
            return TimeQueryType.YEAR;
        }
        return null;
    }


    @ApiOperation(value = "日交易光伏和风电", notes = "根据时间参数和电站ID获取数据，支持日/月/年查询")
    @GetMapping("/getEnergyNewDailyClean")
    public Result<Object> getEnergyNewDailyClean(
            @ApiParam(value = "电站ID", required = true) @RequestParam Long stationId,
            @ApiParam(value = "时间参数 (yyyy-MM-dd/yyyy-MM/yyyy格式)", required = true) @RequestParam String date,
            @ApiParam(value = "查询类型", required = true) @RequestParam Integer dimension,
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId) {
        try {
            // 参数验证
            if (stationId == null || stationId <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (date == null || date.trim().isEmpty()) {
                return Result.error("时间参数不能为空");
            }

            // 解析时间参数类型
            TimeQueryType queryType = parseTimeQueryType(date);
            if (queryType == null) {
                return Result.error("时间格式不正确，支持格式：yyyy-MM-dd（日）、yyyy-MM（月）、yyyy（年）");
            }

            log.info("查询新能源日清分数据 - 电站ID: {}, 省份ID: {}, 时间参数: {}, 查询类型: {}",
                    stationId, provinceId, date, queryType);

            // 切换数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + provinceId);
            }
            DynamicDataSourceContextHolder.push(dsKey);

            // 验证电站
            Station station = stationService.getById(stationId);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            // 根据查询类型执行不同的查询逻辑
            Object result = null;
            switch (dimension) {
                case 1:
                    // 年度查询
                    result = queryEnergyNewYearlyData(stationId, date);
                    break;
                case 2:
                    // 月度查询
                    result = queryEnergyNewMonthlyData(stationId, date);
                    break;
                case 3:
                    // 日度查询
                    result = queryEnergyNewDailyData(stationId, date);
                    break;
            }

            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询新能源日清分数据失败 - 电站ID: {}, 时间参数: {}", stationId, date, e);
            return Result.error("查询失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    // 查询单日数据
    private EnergyNewDailyCleanWithRolloverDTO queryEnergyNewDailyData(Long stationId, String date) {
        try {
            return energyNewDailyCleanService.selectByMonthWithRollover(stationId, date);
        } catch (Exception e) {
            log.error("查询单日数据异常 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new EnergyNewDailyCleanWithRolloverDTO();
        }
    }

    // 查询月度数据（返回当月每天的数据）
    private List<EnergyNewDailyCleanWithRolloverDTO> queryEnergyNewMonthlyData(Long stationId, String yearMonth) {
        try {
            // 解析年月
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 获取当月的第一天和最后一天
            LocalDate startDate = LocalDate.of(year, month, 1);
            LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());

            List<EnergyNewDailyCleanWithRolloverDTO> monthlyData = new ArrayList<>();

            // 遍历当月每一天
            for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                EnergyNewDailyCleanWithRolloverDTO dayData = energyNewDailyCleanService.selectByMonthWithRollover(stationId, dateStr);
                if (dayData == null) {
                    dayData = new EnergyNewDailyCleanWithRolloverDTO();
                }
                monthlyData.add(dayData);
            }

            return monthlyData;
        } catch (Exception e) {
            log.error("查询月度数据异常 - 电站ID: {}, 年月: {}", stationId, yearMonth, e);
            return new ArrayList<>();
        }
    }

    // 查询年度数据（返回当年每月每日的数据）
    private Map<String, List<EnergyNewDailyCleanWithRolloverDTO>> queryEnergyNewYearlyData(Long stationId, String year) {
        try {
            int yearInt = Integer.parseInt(year);
            Map<String, List<EnergyNewDailyCleanWithRolloverDTO>> yearlyData = new LinkedHashMap<>();

            // 遍历12个月
            for (int month = 1; month <= 12; month++) {
                String yearMonth = String.format("%d-%02d", yearInt, month);
                List<EnergyNewDailyCleanWithRolloverDTO> monthData = queryEnergyNewMonthlyData(stationId, yearMonth);
                yearlyData.put(yearMonth, monthData);
            }

            return yearlyData;
        } catch (Exception e) {
            log.error("查询年度数据异常 - 电站ID: {}, 年份: {}", stationId, year, e);
            return new HashMap<>();
        }
    }

    /**
     * 储能日清洁数据
     */
    @ApiOperation(value = "日交易储能", notes = "根据时间参数和电站ID获取储能数据，支持日/月/年查询")
    @GetMapping("/getEnergyStorageDailyClean")
    public Result<Object> getEnergyStorageDailyClean(
            @ApiParam(value = "电站ID", required = true) @RequestParam Long stationId,
            @ApiParam(value = "时间参数 (yyyy-MM-dd/yyyy-MM/yyyy格式)", required = true) @RequestParam String date,
            @ApiParam(value = "查询维度") @RequestParam Integer dimension,
            @ApiParam(value = "省份ID (0-全国, 1-安徽, 2-江苏)", required = true) @RequestParam Integer provinceId) {

        try {
            // 参数验证
            if (stationId == null || stationId <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (date == null || date.trim().isEmpty()) {
                return Result.error("时间参数不能为空");
            }

            // 解析时间参数类型
            TimeQueryType queryType = parseTimeQueryType(date);
            if (queryType == null) {
                return Result.error("时间格式不正确，支持格式：yyyy-MM-dd（日）、yyyy-MM（月）、yyyy（年）");
            }

            log.info("查询储能日清分数据 - 电站ID: {}, 省份ID: {}, 时间参数: {}, 查询类型: {}",
                    stationId, provinceId, date, queryType);

            // 切换数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + provinceId);
            }
            DynamicDataSourceContextHolder.push(dsKey);

            // 验证电站
            Station station = stationService.getById(stationId);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            // 根据查询类型执行不同的查询逻辑
            Object result = null;
            switch (dimension) {
                case 1:
                    // 年度查询
                    result = queryEnergyStorageYearlyData(stationId, date);
                    break;
                case 2:
                    // 月度查询
                    result = queryEnergyStorageMonthlyData(stationId, date);
                    break;
                case 3:
                    // 日度查询
                    result = queryEnergyStorageDailyData(stationId, date);
                    break;
            }

            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询储能日清分数据失败 - 电站ID: {}, 时间参数: {}", stationId, date, e);
            return Result.error("查询失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    // 查询单日储能数据
    private EnergyStorageDailyClean queryEnergyStorageDailyData(Long stationId, String date) {
        try {
            LambdaQueryWrapper<EnergyStorageDailyClean> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EnergyStorageDailyClean::getStationId, stationId)
                    .eq(EnergyStorageDailyClean::getDate, date);
            EnergyStorageDailyClean result = energyStorageDailyCleanService.getOne(queryWrapper);
            return result != null ? result : new EnergyStorageDailyClean();
        } catch (Exception e) {
            log.error("查询单日储能数据异常 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new EnergyStorageDailyClean();
        }
    }

    // 查询月度储能数据
    private List<EnergyStorageDailyClean> queryEnergyStorageMonthlyData(Long stationId, String yearMonth) {
        try {
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            LocalDate startDate = LocalDate.of(year, month, 1);
            LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());

            List<EnergyStorageDailyClean> monthlyData = new ArrayList<>();

            for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                EnergyStorageDailyClean dayData = queryEnergyStorageDailyData(stationId, dateStr);
                if (dayData.getDate() == null) {
                    dayData = new EnergyStorageDailyClean();
                }
                monthlyData.add(dayData);
            }

            return monthlyData;
        } catch (Exception e) {
            log.error("查询月度储能数据异常 - 电站ID: {}, 年月: {}", stationId, yearMonth, e);
            return new ArrayList<>();
        }
    }

    // 查询年度储能数据
    private Map<String, List<EnergyStorageDailyClean>> queryEnergyStorageYearlyData(Long stationId, String year) {
        try {
            int yearInt = Integer.parseInt(year);
            Map<String, List<EnergyStorageDailyClean>> yearlyData = new LinkedHashMap<>();

            for (int month = 1; month <= 12; month++) {
                String yearMonth = String.format("%d-%02d", yearInt, month);
                List<EnergyStorageDailyClean> monthData = queryEnergyStorageMonthlyData(stationId, yearMonth);
                yearlyData.put(yearMonth, monthData);
            }

            return yearlyData;
        } catch (Exception e) {
            log.error("查询年度储能数据异常 - 电站ID: {}, 年份: {}", stationId, year, e);
            return new HashMap<>();
        }
    }

}