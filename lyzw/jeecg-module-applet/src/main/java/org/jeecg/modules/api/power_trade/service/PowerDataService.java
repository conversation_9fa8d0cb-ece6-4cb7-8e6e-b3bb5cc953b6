package org.jeecg.modules.api.power_trade.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power_trade.entity.FileStationRelation;
import org.jeecg.modules.api.power_trade.entity.PowerSideSettle;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一的电力数据服务
 * 避免循环依赖，提供统一的数据获取逻辑
 */
@Slf4j
@Service
public class PowerDataService {

    @Autowired
    private org.jeecg.modules.api.power.service.impl.PowerService powerService;

    @Autowired
    private PowerSideSettleService powerSideSettleService;

    @Autowired
    private FileStationRelationService fileStationRelationService;

    @Autowired
    private YearlyPowerPlanService yearlyPowerPlanService;

    /**
     * 获取电站累计发电量（使用PowerService）
     */
    public BigDecimal getStationTotalGeneration(Long stationId, String year) {
        try {
            log.debug("使用PowerService获取电站{}年度{}累计发电量", stationId, year);

            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setTimeDimension("1"); // 年维度
            param.setQueryDate(year);

            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            if (trendData == null || trendData.isEmpty()) {
                log.warn("电站{}年度{}无发电量数据", stationId, year);
                return BigDecimal.ZERO;
            }

            double totalGeneration = trendData.stream()
                    .map(PowerGenerationTrendDto::getPowerGeneration)
                    .filter(Objects::nonNull)
                    .reduce(0.0, Double::sum);

            BigDecimal result = BigDecimal.valueOf(totalGeneration);
            log.debug("电站{}年度{}累计发电量: {} MWh", stationId, year, result);

            return result;

        } catch (Exception e) {
            log.error("使用PowerService获取电站{}累计发电量失败: {}", stationId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取电站结算电费
     */
    public BigDecimal getStationSettlementElectricFee(Long stationId, String year) {
        try {
            log.debug("获取电站{}年度{}结算电费", stationId, year);

            LocalDate firstDayOfYear = LocalDate.of(Integer.parseInt(year), 1, 1);
            LocalDate lastDayOfYear = firstDayOfYear.with(TemporalAdjusters.lastDayOfYear());

            List<FileStationRelation> fileStationRelations = fileStationRelationService.list(
                    new LambdaQueryWrapper<FileStationRelation>()
                            .eq(FileStationRelation::getStationId, stationId)
                            .ge(FileStationRelation::getSettleDate, firstDayOfYear)
                            .le(FileStationRelation::getSettleDate, lastDayOfYear)
            );

            if (fileStationRelations.isEmpty()) {
                log.debug("电站{}年度{}无FileStationRelation数据", stationId, year);
                return BigDecimal.ZERO;
            }

            List<Long> fileIds = fileStationRelations.stream()
                    .map(FileStationRelation::getId)
                    .collect(Collectors.toList());

            List<PowerSideSettle> powerSideSettles = powerSideSettleService.list(
                    new LambdaQueryWrapper<PowerSideSettle>()
                            .in(PowerSideSettle::getFileId, fileIds)
            );

            BigDecimal totalSettlementElectricFee = powerSideSettles.stream()
                    .map(PowerSideSettle::getSettlementElectricFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("电站{}年度{}结算电费: {} 元", stationId, year, totalSettlementElectricFee);
            return totalSettlementElectricFee;

        } catch (Exception e) {
            log.error("获取电站{}结算电费失败: {}", stationId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取电站计划发电量
     */
    public BigDecimal getStationPlanPower(Long stationId, String year) {
        try {
            String currentYear = year != null ? year : String.valueOf(java.time.Year.now().getValue());

            LambdaQueryWrapper<YearlyPowerPlan> queryWrapper =
                    new LambdaQueryWrapper<>();
            queryWrapper.eq(YearlyPowerPlan::getStationId, stationId)
                    .eq(YearlyPowerPlan::getYear, currentYear);

            List<YearlyPowerPlan> planList = yearlyPowerPlanService.list(queryWrapper);

            return planList.stream()
                    .map(YearlyPowerPlan::getPlanValue)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.warn("获取电站{}计划发电量失败: {}", stationId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取电站统一数据（发电量 + 结算电费 + 计划发电量）
     */
    public Map<String, Object> getStationUnifiedData(Long stationId, String year) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取累计发电量（PowerService）
            BigDecimal totalGeneration = getStationTotalGeneration(stationId, year);

            // 2. 获取结算电费（PowerSideSettle）
            BigDecimal totalSettlementElectricFee = getStationSettlementElectricFee(stationId, year);

            // 3. 获取计划发电量（YearlyPowerPlan）
            BigDecimal planPower = getStationPlanPower(stationId, year);

            // 4. 计算交易均价
            BigDecimal avgPrice = BigDecimal.ZERO;
            if (totalGeneration.compareTo(BigDecimal.ZERO) > 0 && totalSettlementElectricFee.compareTo(BigDecimal.ZERO) > 0) {
                avgPrice = totalSettlementElectricFee.divide(totalGeneration, 6, BigDecimal.ROUND_HALF_UP);
            }

            // 5. 构建返回数据
            result.put("totalSettlementElectricity", totalGeneration);
            result.put("totalSettlementElectricFee", totalSettlementElectricFee);
            result.put("averagePrice", avgPrice);
            result.put("current_month_power", planPower);
            result.put("settlementRecordCount", totalGeneration.compareTo(BigDecimal.ZERO) > 0 ? 1 : 0);

            log.debug("电站{}统一数据获取完成 - 发电量: {} MWh, 结算电费: {} 元, 交易均价: {} 元/MWh, 计划电量: {} MWh",
                    stationId, totalGeneration, totalSettlementElectricFee, avgPrice, planPower);

        } catch (Exception e) {
            log.error("获取电站{}统一数据失败: {}", stationId, e.getMessage());
            // 设置默认值
            result.put("totalSettlementElectricity", BigDecimal.ZERO);
            result.put("totalSettlementElectricFee", BigDecimal.ZERO);
            result.put("averagePrice", BigDecimal.ZERO);
            result.put("current_month_power", BigDecimal.ZERO);
            result.put("settlementRecordCount", 0);
        }

        return result;
    }
}
