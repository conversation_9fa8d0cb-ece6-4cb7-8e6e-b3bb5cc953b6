package org.jeecg.modules.api.power_trade.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power_trade.entity.FileStationRelation;
import org.jeecg.modules.api.power_trade.entity.PowerSideSettle;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一的电力数据服务
 * 避免循环依赖，提供统一的数据获取逻辑
 */
@Slf4j
@Service
public class PowerDataService {

    @Autowired
    private org.jeecg.modules.api.power.service.impl.PowerService powerService;

    @Autowired
    private PowerSideSettleService powerSideSettleService;

    @Autowired
    private FileStationRelationService fileStationRelationService;

    @Autowired
    private YearlyPowerPlanService yearlyPowerPlanService;

    /**
     * 获取电站累计发电量（使用PowerService）
     */
    public BigDecimal getStationTotalGeneration(Long stationId, String year) {
        try {
            log.info("【调试】使用PowerService获取电站{}年度{}累计发电量", stationId, year);

            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setTimeDimension("1"); // 年维度
            param.setQueryDate(year);

            log.info("【调试】PowerService查询参数 - 电站ID: {}, 时间维度: {}, 查询日期: {}",
                    param.getStationId(), param.getTimeDimension(), param.getQueryDate());

            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            log.info("【调试】PowerService返回数据 - 数据条数: {}",
                    trendData != null ? trendData.size() : 0);

            if (trendData == null || trendData.isEmpty()) {
                log.warn("【调试】电站{}年度{}无发电量数据，PowerService返回空数据", stationId, year);
                return BigDecimal.ZERO;
            }

            // 打印前几条数据用于调试
            if (trendData.size() > 0) {
                log.info("【调试】PowerService返回数据示例 - 第一条: timeLabel={}, powerGeneration={}",
                        trendData.get(0).getTimeLabel(), trendData.get(0).getPowerGeneration());
            }

            double totalGeneration = trendData.stream()
                    .map(PowerGenerationTrendDto::getPowerGeneration)
                    .filter(Objects::nonNull)
                    .reduce(0.0, Double::sum);

            BigDecimal result = BigDecimal.valueOf(totalGeneration);
            log.info("【调试】电站{}年度{}累计发电量计算完成: {} MWh (原始数据条数: {})",
                    stationId, year, result, trendData.size());

            return result;

        } catch (Exception e) {
            log.error("【调试】使用PowerService获取电站{}累计发电量失败: {}", stationId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取电站结算电费
     */
    public BigDecimal getStationSettlementElectricFee(Long stationId, String year) {
        try {
            log.info("【调试】获取电站{}年度{}结算电费", stationId, year);

            LocalDate firstDayOfYear = LocalDate.of(Integer.parseInt(year), 1, 1);
            LocalDate lastDayOfYear = firstDayOfYear.with(TemporalAdjusters.lastDayOfYear());

            log.info("【调试】查询FileStationRelation - 电站ID: {}, 时间范围: {} 到 {}",
                    stationId, firstDayOfYear, lastDayOfYear);

            List<FileStationRelation> fileStationRelations = fileStationRelationService.list(
                    new LambdaQueryWrapper<FileStationRelation>()
                            .eq(FileStationRelation::getStationId, stationId)
                            .ge(FileStationRelation::getSettleDate, firstDayOfYear)
                            .le(FileStationRelation::getSettleDate, lastDayOfYear)
            );

            log.info("【调试】FileStationRelation查询结果 - 数据条数: {}",
                    fileStationRelations != null ? fileStationRelations.size() : 0);

            if (fileStationRelations.isEmpty()) {
                log.warn("【调试】电站{}年度{}无FileStationRelation数据", stationId, year);
                return BigDecimal.ZERO;
            }

            List<Long> fileIds = fileStationRelations.stream()
                    .map(FileStationRelation::getId)
                    .collect(Collectors.toList());

            log.info("【调试】提取到的FileId列表: {}", fileIds);

            List<PowerSideSettle> powerSideSettles = powerSideSettleService.list(
                    new LambdaQueryWrapper<PowerSideSettle>()
                            .in(PowerSideSettle::getFileId, fileIds)
            );

            log.info("【调试】PowerSideSettle查询结果 - 数据条数: {}",
                    powerSideSettles != null ? powerSideSettles.size() : 0);

            if (powerSideSettles != null && !powerSideSettles.isEmpty()) {
                // 打印前几条数据用于调试
                PowerSideSettle firstSettle = powerSideSettles.get(0);
                log.info("【调试】PowerSideSettle数据示例 - fileId: {}, settlementElectricFee: {}",
                        firstSettle.getFileId(), firstSettle.getSettlementElectricFee());
            }

            BigDecimal totalSettlementElectricFee = powerSideSettles.stream()
                    .map(PowerSideSettle::getSettlementElectricFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("【调试】电站{}年度{}结算电费计算完成: {} 元 (原始数据条数: {})",
                    stationId, year, totalSettlementElectricFee, powerSideSettles.size());
            return totalSettlementElectricFee;

        } catch (Exception e) {
            log.error("【调试】获取电站{}结算电费失败: {}", stationId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取电站计划发电量
     */
    public BigDecimal getStationPlanPower(Long stationId, String year) {
        try {
            String currentYear = year != null ? year : String.valueOf(java.time.Year.now().getValue());

            log.info("【调试】获取电站{}年度{}计划发电量", stationId, currentYear);

            LambdaQueryWrapper<YearlyPowerPlan> queryWrapper =
                    new LambdaQueryWrapper<>();
            queryWrapper.eq(YearlyPowerPlan::getStationId, stationId)
                    .eq(YearlyPowerPlan::getYear, currentYear);

            List<YearlyPowerPlan> planList = yearlyPowerPlanService.list(queryWrapper);

            log.info("【调试】YearlyPowerPlan查询结果 - 数据条数: {}",
                    planList != null ? planList.size() : 0);

            if (planList != null && !planList.isEmpty()) {
                // 打印前几条数据用于调试
                YearlyPowerPlan firstPlan = planList.get(0);
                log.info("【调试】YearlyPowerPlan数据示例 - stationId: {}, year: {}, planValue: {}",
                        firstPlan.getStationId(), firstPlan.getYear(), firstPlan.getPlanValue());
            }

            BigDecimal totalPlanPower = planList.stream()
                    .map(YearlyPowerPlan::getPlanValue)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("【调试】电站{}年度{}计划发电量计算完成: {} MWh (原始数据条数: {})",
                    stationId, currentYear, totalPlanPower, planList.size());

            return totalPlanPower;

        } catch (Exception e) {
            log.error("【调试】获取电站{}计划发电量失败: {}", stationId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取电站统一数据（发电量 + 结算电费 + 计划发电量）
     */
    public Map<String, Object> getStationUnifiedData(Long stationId, String year) {
        Map<String, Object> result = new HashMap<>();

        log.info("【调试】=== 开始获取电站{}年度{}统一数据 ===", stationId, year);

        try {
            // 1. 获取累计发电量（PowerService）
            log.info("【调试】步骤1: 获取累计发电量");
            BigDecimal totalGeneration = getStationTotalGeneration(stationId, year);
            log.info("【调试】步骤1完成 - 累计发电量: {} MWh", totalGeneration);

            // 2. 获取结算电费（PowerSideSettle）
            log.info("【调试】步骤2: 获取结算电费");
            BigDecimal totalSettlementElectricFee = getStationSettlementElectricFee(stationId, year);
            log.info("【调试】步骤2完成 - 结算电费: {} 元", totalSettlementElectricFee);

            // 3. 获取计划发电量（YearlyPowerPlan）
            log.info("【调试】步骤3: 获取计划发电量");
            BigDecimal planPower = getStationPlanPower(stationId, year);
            log.info("【调试】步骤3完成 - 计划发电量: {} MWh", planPower);

            // 4. 计算交易均价
            log.info("【调试】步骤4: 计算交易均价");
            BigDecimal avgPrice = BigDecimal.ZERO;
            if (totalGeneration.compareTo(BigDecimal.ZERO) > 0 && totalSettlementElectricFee.compareTo(BigDecimal.ZERO) > 0) {
                avgPrice = totalSettlementElectricFee.divide(totalGeneration, 6, BigDecimal.ROUND_HALF_UP);
                log.info("【调试】交易均价计算: {} ÷ {} = {} 元/MWh", totalSettlementElectricFee, totalGeneration, avgPrice);
            } else {
                log.info("【调试】交易均价为0 - 发电量: {}, 结算电费: {}", totalGeneration, totalSettlementElectricFee);
            }

            // 5. 构建返回数据
            log.info("【调试】步骤5: 构建返回数据");
            result.put("totalSettlementElectricity", totalGeneration);  // 结算电量（实际是发电量）
            result.put("accumulatedPower", totalGeneration);            // 累计发电量（为了兼容性）
            result.put("totalSettlementElectricFee", totalSettlementElectricFee);
            result.put("averagePrice", avgPrice);
            result.put("current_month_power", planPower);
            result.put("settlementRecordCount", totalGeneration.compareTo(BigDecimal.ZERO) > 0 ? 1 : 0);

            log.info("【调试】=== 电站{}统一数据获取完成 ===", stationId);
            log.info("【调试】最终结果 - 累计发电量: {} MWh, 结算电量: {} MWh, 结算电费: {} 元, 交易均价: {} 元/MWh, 计划电量: {} MWh",
                    totalGeneration, totalGeneration, totalSettlementElectricFee, avgPrice, planPower);

        } catch (Exception e) {
            log.error("【调试】获取电站{}统一数据失败: {}", stationId, e.getMessage(), e);
            // 设置默认值
            result.put("totalSettlementElectricity", BigDecimal.ZERO);
            result.put("totalSettlementElectricFee", BigDecimal.ZERO);
            result.put("averagePrice", BigDecimal.ZERO);
            result.put("current_month_power", BigDecimal.ZERO);
            result.put("settlementRecordCount", 0);

            log.info("【调试】使用默认值 - 所有数据均为0");
        }

        return result;
    }
}
