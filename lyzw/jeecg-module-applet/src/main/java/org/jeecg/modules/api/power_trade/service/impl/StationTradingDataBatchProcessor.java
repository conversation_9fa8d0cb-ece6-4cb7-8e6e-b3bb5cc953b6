package org.jeecg.modules.api.power_trade.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.*;
import org.jeecg.modules.api.power_trade.service.FileStationRelationService;
import org.jeecg.modules.api.power_trade.service.PowerSideSettleService;
import org.jeecg.modules.api.power_trade.service.PowerDataService;
import org.jeecg.modules.api.power_trade.service.YearlyPowerPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电站交易数据批量处理器
 * 使用统一的数据获取逻辑，解决N+1查询问题，提升查询性能
 *
 * <AUTHOR> Team
 * @since 2025-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StationTradingDataBatchProcessor {

    private final YearlyPowerPlanService yearlyPowerPlanService;
    private final FileStationRelationService fileStationRelationService;
    private final PowerSideSettleService powerSideSettleService;

    @Autowired
    private PowerDataService powerDataService;

    /**
     * 批量获取多个电站的统一结算数据
     * 使用统一的数据获取逻辑：YearlyPowerPlan + FileStationRelation + PowerSideSettle
     *
     * @param stationIds 电站ID列表
     * @param year       年份
     * @param month      月份（可选）
     * @return 按电站ID分组的统一结算数据
     */
    public Map<Long, Map<String, Object>> batchGetUnifiedSettlementData(
            List<Long> stationIds, String year, String month) {

        if (stationIds == null || stationIds.isEmpty()) {
            return new HashMap<>();
        }

        log.debug("批量查询统一结算数据 - 电站数量: {}, 年份: {}, 月份: {}", stationIds.size(), year, month);

        try {
            Map<Long, Map<String, Object>> result = new HashMap<>();

            // 1. 批量获取计划发电量数据
            Map<Long, BigDecimal> planPowerMap = batchGetPlanPowerData(stationIds, year, month);

            // 2. 批量获取FileStationRelation数据
            Map<Long, List<FileStationRelation>> fileRelationMap = batchGetFileStationRelations(stationIds, year, month);

            // 3. 批量获取PowerSideSettle数据
            Map<Long, List<PowerSideSettle>> settleDataMap = batchGetPowerSideSettleData(fileRelationMap);

            // 4. 为每个电站构建统一的结算数据
            for (Long stationId : stationIds) {
                Map<String, Object> stationData = buildUnifiedStationData(
                    stationId, year, month,
                    planPowerMap.getOrDefault(stationId, BigDecimal.ZERO),
                    settleDataMap.getOrDefault(stationId, new ArrayList<>())
                );
                result.put(stationId, stationData);
            }

            log.debug("批量查询统一结算数据完成 - 处理了{}个电站", result.size());
            return result;

        } catch (Exception e) {
            log.error("批量查询统一结算数据失败 - 电站数量: {}", stationIds.size(), e);
            return new HashMap<>();
        }
    }

    /**
     * 批量构建电站交易概况数据（使用统一数据获取逻辑）
     *
     * @param stations  电站列表
     * @param dimension 查询维度
     * @param year      年份
     * @param month     月份
     * @return 电站交易概况数据列表
     */
    public List<Map<String, Object>> batchBuildStationTradingOverview(
            List<Station> stations, Integer dimension, String year, String month) {

        if (stations == null || stations.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("批量构建电站交易概况（统一逻辑） - 电站数量: {}, 维度: {}", stations.size(), dimension);

        List<Long> stationIds = stations.stream()
            .map(Station::getId)
            .collect(Collectors.toList());

        // 使用统一的批量数据获取逻辑
        Map<Long, Map<String, Object>> unifiedDataMap =
            batchGetUnifiedSettlementData(stationIds, year, month);

        // 为每个电站构建数据
        return stations.stream()
            .map(station -> buildStationTradingOverviewFromUnifiedData(
                station, dimension, year, month, unifiedDataMap.get(station.getId())))
            .collect(Collectors.toList());
    }

    /**
     * 从批量数据构建单个电站的交易概况
     * 
     * @param station        电站信息
     * @param dimension      查询维度
     * @param year           年份
     * @param month          月份
     * @param settlementList 该电站的结算数据
     * @return 电站交易概况数据
     */
    private Map<String, Object> buildStationTradingOverviewFromBatch(
            Station station, Integer dimension, String year, String month, 
            List<ScreenTradeSettlement> settlementList) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("station", station);

        try {
            if (settlementList == null) {
                settlementList = new ArrayList<>();
            }

            if (dimension == 1 && Objects.nonNull(month)) {
                // 月度查询：返回指定月份的数据
                Map<String, Object> monthlyData = buildMonthlyTradingDataFromBatch(
                    station, year, month, settlementList);
                result.putAll(monthlyData);
                
            } else if (dimension == 2) {
                // 年度查询：返回该年每个月的数据列表
                List<Map<String, Object>> monthlyDataList = buildYearlyTradingDataListFromBatch(
                    station, year, settlementList);
                result.put("monthlyDataList", monthlyDataList);

                // 计算年度汇总数据
                Map<String, Object> yearlyTotal = calculateYearlyTotal(monthlyDataList);
                result.putAll(yearlyTotal);
            }

        } catch (Exception e) {
            log.warn("构建电站{}交易概况失败: {}", station.getId(), e.getMessage());
            // 异常时设置默认值
            setDefaultValues(result, dimension);
        }

        return result;
    }

    /**
     * 从统一数据构建单个电站的交易概况
     *
     * @param station        电站信息
     * @param dimension      查询维度
     * @param year           年份
     * @param month          月份
     * @param unifiedData    统一的结算数据
     * @return 电站交易概况数据
     */
    private Map<String, Object> buildStationTradingOverviewFromUnifiedData(
            Station station, Integer dimension, String year, String month,
            Map<String, Object> unifiedData) {

        Map<String, Object> result = new HashMap<>();
        result.put("station", station);

        try {
            if (unifiedData == null) {
                unifiedData = new HashMap<>();
            }

            if (dimension == 1 && Objects.nonNull(month)) {
                // 月度查询：直接使用统一数据
                result.putAll(unifiedData);

            } else if (dimension == 2) {
                // 年度查询：需要构建12个月的数据列表
                // 这里简化处理，实际应该调用统一的年度数据构建逻辑
                List<Map<String, Object>> monthlyDataList = new ArrayList<>();
                for (int i = 1; i <= 12; i++) {
                    Map<String, Object> monthData = new HashMap<>();
                    monthData.put("year", year);
                    monthData.put("month", String.format("%02d", i));

                    if (month == null || month.equals(String.format("%02d", i))) {
                        // 如果是当前查询的月份，使用实际数据
                        monthData.putAll(unifiedData);
                    } else {
                        // 其他月份设置默认值
                        setDefaultMonthlyValues(monthData);
                    }
                    monthlyDataList.add(monthData);
                }

                result.put("monthlyDataList", monthlyDataList);

                // 计算年度汇总数据
                Map<String, Object> yearlyTotal = calculateYearlyTotalFromUnified(monthlyDataList);
                result.putAll(yearlyTotal);
            }

        } catch (Exception e) {
            log.warn("构建电站{}交易概况失败: {}", station.getId(), e.getMessage());
            // 异常时设置默认值
            setDefaultValues(result, dimension);
        }

        return result;
    }

    /**
     * 从批量数据构建单月交易数据
     */
    private Map<String, Object> buildMonthlyTradingDataFromBatch(
            Station station, String year, String month, List<ScreenTradeSettlement> allSettlements) {
        
        Map<String, Object> monthData = new HashMap<>();

        // 过滤出指定月份的数据
        List<ScreenTradeSettlement> monthSettlements = allSettlements.stream()
            .filter(settlement -> month.equals(settlement.getMonth()))
            .collect(Collectors.toList());

        if (!monthSettlements.isEmpty()) {
            // 计算月度汇总数据
            MonthlyAggregateData aggregateData = calculateMonthlyAggregate(monthSettlements);
            
            monthData.put("year", year);
            monthData.put("month", month);
            monthData.put("totalElectricity", aggregateData.getCurrentMonthPlanPower());
            monthData.put("current_month_power", aggregateData.getCurrentMonthPower());
            monthData.put("averagePrice", aggregateData.getAveragePrice());
            monthData.put("settlementList", monthSettlements);

        } else {
            // 无数据时设置默认值
            setDefaultMonthlyValues(monthData, year, month);
        }

        return monthData;
    }

    /**
     * 从批量数据构建年度每月交易数据列表
     */
    private List<Map<String, Object>> buildYearlyTradingDataListFromBatch(
            Station station, String year, List<ScreenTradeSettlement> allSettlements) {
        
        List<Map<String, Object>> monthlyDataList = new ArrayList<>();

        // 按月份分组
        Map<String, List<ScreenTradeSettlement>> monthlyGrouped = allSettlements.stream()
            .collect(Collectors.groupingBy(ScreenTradeSettlement::getMonth));

        // 为每个月生成数据（1-12月）
        for (int monthNum = 1; monthNum <= 12; monthNum++) {
            String monthStr = String.format("%02d", monthNum);
            List<ScreenTradeSettlement> monthSettlements = monthlyGrouped.getOrDefault(monthStr, new ArrayList<>());

            Map<String, Object> monthData = new HashMap<>();
            monthData.put("year", year);
            monthData.put("month", monthStr);

            if (!monthSettlements.isEmpty()) {
                MonthlyAggregateData aggregateData = calculateMonthlyAggregate(monthSettlements);
                
                monthData.put("totalElectricity", aggregateData.getCurrentMonthPlanPower());
                monthData.put("current_month_power", aggregateData.getCurrentMonthPower());
                monthData.put("averagePrice", aggregateData.getAveragePrice());
                monthData.put("recordCount", monthSettlements.size());

            } else {
                // 该月无数据，设置为0
                monthData.put("totalElectricity", BigDecimal.ZERO);
                monthData.put("current_month_power", BigDecimal.ZERO);
                monthData.put("averagePrice", BigDecimal.ZERO);
                monthData.put("recordCount", 0);
            }

            monthlyDataList.add(monthData);
        }

        return monthlyDataList;
    }

    /**
     * 计算月度聚合数据
     */
    private MonthlyAggregateData calculateMonthlyAggregate(List<ScreenTradeSettlement> settlements) {
        BigDecimal totalElectricity = settlements.stream()
            .map(ScreenTradeSettlement::getSettlePower)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalFee = settlements.stream()
            .map(ScreenTradeSettlement::getSettleElectricityFee)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal currentMonthPower = settlements.stream()
            .map(ScreenTradeSettlement::getCurrentMonthPower)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal currentMonthPlanPower = settlements.stream()
            .map(ScreenTradeSettlement::getCurrentMonthPlanPower)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 交易均价 = 总电费 ÷ 总电量
        BigDecimal averagePrice = totalElectricity.compareTo(BigDecimal.ZERO) > 0 ?
            totalFee.divide(totalElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        return new MonthlyAggregateData(totalElectricity, totalFee, currentMonthPower, 
                                       currentMonthPlanPower, averagePrice);
    }

    /**
     * 计算年度汇总数据
     */
    private Map<String, Object> calculateYearlyTotal(List<Map<String, Object>> monthlyDataList) {
        Map<String, Object> yearlyTotal = new HashMap<>();

        BigDecimal totalElectricity = monthlyDataList.stream()
            .map(data -> (BigDecimal) data.get("totalElectricity"))
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCurrentMonthPower = monthlyDataList.stream()
            .map(data -> (BigDecimal) data.get("current_month_power"))
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 年度平均电价：所有月份的加权平均
        BigDecimal totalFee = BigDecimal.ZERO;
        BigDecimal totalPower = BigDecimal.ZERO;

        for (Map<String, Object> monthData : monthlyDataList) {
            BigDecimal monthElectricity = (BigDecimal) monthData.get("totalElectricity");
            BigDecimal monthPrice = (BigDecimal) monthData.get("averagePrice");

            if (monthElectricity != null && monthPrice != null &&
                monthElectricity.compareTo(BigDecimal.ZERO) > 0) {
                totalFee = totalFee.add(monthElectricity.multiply(monthPrice));
                totalPower = totalPower.add(monthElectricity);
            }
        }

        BigDecimal yearlyAveragePrice = totalPower.compareTo(BigDecimal.ZERO) > 0 ?
            totalFee.divide(totalPower, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        yearlyTotal.put("totalElectricity", totalElectricity);
        yearlyTotal.put("current_month_power", totalCurrentMonthPower);
        yearlyTotal.put("averagePrice", yearlyAveragePrice);

        return yearlyTotal;
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(Map<String, Object> result, Integer dimension) {
        result.put("totalElectricity", BigDecimal.ZERO);
        result.put("averagePrice", BigDecimal.ZERO);
        result.put("current_month_power", BigDecimal.ZERO);
        if (dimension == 2) {
            result.put("monthlyDataList", new ArrayList<>());
        }
    }

    /**
     * 设置默认月度值
     */
    private void setDefaultMonthlyValues(Map<String, Object> monthData, String year, String month) {
        monthData.put("year", year);
        monthData.put("month", month);
        monthData.put("totalElectricity", BigDecimal.ZERO);
        monthData.put("current_month_power", BigDecimal.ZERO);
        monthData.put("averagePrice", BigDecimal.ZERO);
        monthData.put("settlementList", new ArrayList<>());
    }

    /**
     * 月度聚合数据内部类
     */
    private static class MonthlyAggregateData {
        private final BigDecimal totalElectricity;
        private final BigDecimal totalFee;
        private final BigDecimal currentMonthPower;
        private final BigDecimal currentMonthPlanPower;
        private final BigDecimal averagePrice;

        public MonthlyAggregateData(BigDecimal totalElectricity, BigDecimal totalFee,
                                   BigDecimal currentMonthPower, BigDecimal currentMonthPlanPower,
                                   BigDecimal averagePrice) {
            this.totalElectricity = totalElectricity;
            this.totalFee = totalFee;
            this.currentMonthPower = currentMonthPower;
            this.currentMonthPlanPower = currentMonthPlanPower;
            this.averagePrice = averagePrice;
        }

        public BigDecimal getTotalElectricity() { return totalElectricity; }
        public BigDecimal getTotalFee() { return totalFee; }
        public BigDecimal getCurrentMonthPower() { return currentMonthPower; }
        public BigDecimal getCurrentMonthPlanPower() { return currentMonthPlanPower; }
        public BigDecimal getAveragePrice() { return averagePrice; }
    }

    // ==================== 统一数据获取辅助方法 ====================

    /**
     * 批量获取计划发电量数据
     */
    private Map<Long, BigDecimal> batchGetPlanPowerData(List<Long> stationIds, String year, String month) {
        LambdaQueryWrapper<YearlyPowerPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(YearlyPowerPlan::getStationId, stationIds)
                .eq(YearlyPowerPlan::getYear, year);

        if (month != null && !month.trim().isEmpty()) {
            queryWrapper.eq(YearlyPowerPlan::getMonth, month);
        }

        List<YearlyPowerPlan> planList = yearlyPowerPlanService.list(queryWrapper);

        return planList.stream()
                .collect(Collectors.groupingBy(
                        YearlyPowerPlan::getStationId,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                YearlyPowerPlan::getPlanValue,
                                BigDecimal::add
                        )
                ));
    }

    /**
     * 批量获取FileStationRelation数据
     */
    private Map<Long, List<FileStationRelation>> batchGetFileStationRelations(List<Long> stationIds, String year, String month) {
        LocalDate startDate, endDate;

        if (month != null && !month.trim().isEmpty()) {
            // 月度查询
            startDate = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
            endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
        } else {
            // 年度查询
            startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
            endDate = startDate.with(TemporalAdjusters.lastDayOfYear());
        }

        LambdaQueryWrapper<FileStationRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FileStationRelation::getStationId, stationIds)
                .ge(FileStationRelation::getSettleDate, startDate)
                .le(FileStationRelation::getSettleDate, endDate);

        List<FileStationRelation> relationList = fileStationRelationService.list(queryWrapper);

        return relationList.stream()
                .collect(Collectors.groupingBy(FileStationRelation::getStationId));
    }

    /**
     * 批量获取PowerSideSettle数据
     */
    private Map<Long, List<PowerSideSettle>> batchGetPowerSideSettleData(Map<Long, List<FileStationRelation>> fileRelationMap) {
        // 收集所有file_id
        List<Long> allFileIds = fileRelationMap.values().stream()
                .flatMap(List::stream)
                .map(FileStationRelation::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (allFileIds.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询PowerSideSettle数据
        LambdaQueryWrapper<PowerSideSettle> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PowerSideSettle::getFileId, allFileIds);

        List<PowerSideSettle> settleList = powerSideSettleService.list(queryWrapper);

        // 按电站ID分组
        Map<Long, List<PowerSideSettle>> result = new HashMap<>();

        for (Map.Entry<Long, List<FileStationRelation>> entry : fileRelationMap.entrySet()) {
            Long stationId = entry.getKey();
            List<FileStationRelation> relations = entry.getValue();

            Set<Long> stationFileIds = relations.stream()
                    .map(FileStationRelation::getId)
                    .collect(Collectors.toSet());

            List<PowerSideSettle> stationSettles = settleList.stream()
                    .filter(settle -> stationFileIds.contains(settle.getFileId()))
                    .collect(Collectors.toList());

            result.put(stationId, stationSettles);
        }

        return result;
    }

    /**
     * 构建统一的电站数据（使用PowerDataService获取统一数据）
     */
    private Map<String, Object> buildUnifiedStationData(Long stationId, String year, String month,
                                                        BigDecimal planPower, List<PowerSideSettle> settleList) {
        try {
            log.debug("批量处理器使用PowerDataService获取电站{}统一数据", stationId);

            // 直接使用PowerDataService获取统一数据
            Map<String, Object> unifiedData = powerDataService.getStationUnifiedData(stationId, year);

            // 如果PowerDataService返回的计划发电量为0，使用传入的planPower
            BigDecimal servicePlanPower = (BigDecimal) unifiedData.get("current_month_power");
            if (servicePlanPower.compareTo(BigDecimal.ZERO) == 0 && planPower.compareTo(BigDecimal.ZERO) > 0) {
                unifiedData.put("current_month_power", planPower);
                log.debug("使用传入的计划发电量: {} MWh", planPower);
            }

            log.debug("电站{}统一数据获取完成 - 累计发电量: {} MWh, 结算电量: {} MWh, 交易均价: {} 元/MWh, 计划电量: {} MWh",
                    stationId,
                    unifiedData.get("accumulatedPower"),
                    unifiedData.get("totalSettlementElectricity"),
                    unifiedData.get("averagePrice"),
                    unifiedData.get("current_month_power"));

            return unifiedData;

        } catch (Exception e) {
            log.error("批量处理器获取电站{}统一数据失败，使用默认值: {}", stationId, e.getMessage());

            // 返回默认数据
            Map<String, Object> defaultData = new HashMap<>();
            defaultData.put("current_month_power", planPower);
            defaultData.put("totalSettlementElectricity", BigDecimal.ZERO);
            defaultData.put("totalSettlementElectricFee", BigDecimal.ZERO);
            defaultData.put("averagePrice", BigDecimal.ZERO);
            defaultData.put("settlementRecordCount", 0);

            return defaultData;
        }
    }

    /**
     * 设置默认的月度数据值
     */
    private void setDefaultMonthlyValues(Map<String, Object> monthData) {
        monthData.put("current_month_power", BigDecimal.ZERO);
        monthData.put("totalSettlementElectricity", BigDecimal.ZERO);
        monthData.put("totalSettlementElectricFee", BigDecimal.ZERO);
        monthData.put("averagePrice", BigDecimal.ZERO);
        monthData.put("settlementRecordCount", 0);
    }

    /**
     * 计算年度汇总数据（从统一数据）
     */
    private Map<String, Object> calculateYearlyTotalFromUnified(List<Map<String, Object>> monthlyDataList) {
        Map<String, Object> yearlyTotal = new HashMap<>();

        BigDecimal totalElectricity = monthlyDataList.stream()
                .map(data -> (BigDecimal) data.get("totalSettlementElectricity"))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCurrentMonthPower = monthlyDataList.stream()
                .map(data -> (BigDecimal) data.get("current_month_power"))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 年度平均电价：所有月份的加权平均
        BigDecimal totalFee = BigDecimal.ZERO;
        BigDecimal totalPower = BigDecimal.ZERO;

        for (Map<String, Object> monthData : monthlyDataList) {
            BigDecimal monthElectricity = (BigDecimal) monthData.get("totalSettlementElectricity");
            BigDecimal monthFee = (BigDecimal) monthData.get("totalSettlementElectricFee");

            if (monthElectricity != null && monthFee != null &&
                    monthElectricity.compareTo(BigDecimal.ZERO) > 0) {
                totalFee = totalFee.add(monthFee);
                totalPower = totalPower.add(monthElectricity);
            }
        }

        BigDecimal yearlyAveragePrice = totalPower.compareTo(BigDecimal.ZERO) > 0 ?
                totalFee.divide(totalPower, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        yearlyTotal.put("totalElectricity", totalElectricity);
        yearlyTotal.put("current_month_power", totalCurrentMonthPower);
        yearlyTotal.put("averagePrice", yearlyAveragePrice);

        return yearlyTotal;
    }

}
